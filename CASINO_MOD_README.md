# Casino Slot Machine Mod

A fun Minecraft Forge mod that adds a casino slot machine with customizable items and spinning animations!

## Features

### 🎮 **Dual Casino Experience**
- **GUI Mode**: Use `/slotmachine` to open the casino interface
- **🏗️ Physical Blocks**: Place Slot Machine Blocks and Casino Buttons in the world!

### 🎰 **Slot Machine Features**
- **Clean, Professional Interface**: No inventory clutter - pure casino experience
- **3 Item Slots**: Watch as items flash and spin between different configurable items
- **Interactive Spin Button**: <PERSON><PERSON> highlights on hover and provides visual feedback
- **🌟 Glow Item Frame Display**: Automatically displays slot results on nearby Glow Item Frames!
- **Easy Configuration**: Easily customize which items appear in the slot machine
- **Animated Spinning**: Smooth spinning animation with configurable duration
- **Win Detection**: Get messages when you hit matching items!
- **Responsive Controls**: ESC to close, click to spin

### 🧱 **Physical Casino Blocks**
- **Slot Machine Block**: Right-click to activate nearby Glow Item Frames
- **Casino Button**: Stylish button that powers on/off and activates slot machines
- **World Integration**: Build actual casino structures in your world!

## How to Use

### 🎮 **GUI Mode (Command-Based)**
1. **Open the Slot Machine**: Type `/slotmachine` in chat to open the casino interface
2. **Spin the Slots**: Click the "SPIN!" button to start the slot machine (button lights up on hover!)
3. **Watch the Animation**: Items will rapidly cycle through the configured list
4. **Check Results**: After spinning stops, you'll get a message about your results:
   - **JACKPOT!**: All three items match
   - **Nice!**: Two items match
   - **Better luck next time!**: No matches
5. **Close the Interface**: Press ESC or click outside to close

### 🏗️ **Physical Block Mode**
1. **Craft Casino Blocks**: Create Slot Machine Blocks and Casino Buttons (recipes in creative tab)
2. **Place Glow Item Frames**: Set up 3 Glow Item Frames in your casino area
3. **Place Casino Blocks**: Put down Slot Machine Blocks or Casino Buttons near the frames
4. **Activate**: Right-click the blocks to start the slot machine spinning on the frames!
5. **Build Your Casino**: Create elaborate casino structures with multiple setups

### 🌟 Glow Item Frame Display
For an enhanced experience, place **3 Glow Item Frames** within 10 blocks of where you use the slot machine:

1. **Place Glow Item Frames**: Set up 3 Glow Item Frames on walls or stands near your casino area
2. **Automatic Detection**: The mod will automatically find and use the 3 closest Glow Item Frames
3. **🎰 Live Animation**: Watch the items rapidly change and spin on the Glow Item Frames during the slot machine animation!
4. **🎯 Perfect Synchronization**: GUI and Glow Item Frames show exactly the same items at all times!
5. **Results Display**: Final results are shown on the frames, creating a spectacular visual display
6. **🔄 Independent Operation**: Frames continue spinning even if you close the slot machine GUI!
7. **💎 Permanent Results**: Final results stay on the frames permanently - no auto-clearing!
8. **🎈 Large Floating Items**: Optional large floating item entities above frames for better visibility!
9. **⚙️ Easy Item Management**: Add/remove items with simple commands - no config editing needed!
10. **🚀 Optimized Performance**: Smooth animation with efficient server-side management
11. **🧹 Manual Clear**: Use `/slotmachine clear` to manually clear frames when needed

## Configuration

The mod includes an easy-to-use configuration system. You can find the config file at:
`config/casinomod-common.toml`

### Customizable Settings:

#### Slot Machine Items
```toml
slotMachineItems = [
    "minecraft:diamond",
    "minecraft:emerald", 
    "minecraft:gold_ingot",
    "minecraft:iron_ingot",
    "minecraft:redstone",
    "minecraft:lapis_lazuli",
    "minecraft:coal",
    "minecraft:apple",
    "minecraft:bread",
    "minecraft:cooked_beef"
]
```

You can add or remove any Minecraft items using their resource location format (e.g., `"minecraft:item_name"`).

#### Spin Duration
```toml
spinDuration = 100
```
Controls how long the spinning animation lasts (in ticks, where 20 ticks = 1 second).

#### Glow Item Frame Display Settings
```toml
# Enable/disable Glow Item Frame display
enableItemFrameDisplay = true

# Range in blocks to search for Glow Item Frames
itemFrameSearchRange = 10

# Clear frames when slot machine is closed (disabled by default - results kept permanently)
clearFramesOnClose = false

# Enable large floating item entities above Glow Item Frames for better visibility
enableFloatingItems = true
```

- **enableItemFrameDisplay**: Turn the Glow Item Frame feature on/off
- **itemFrameSearchRange**: How far (in blocks) to look for Glow Item Frames around the player
- **clearFramesOnClose**: Whether to clear the frames when you close the slot machine interface (disabled by default - results are kept permanently for display)
- **enableFloatingItems**: Enable large floating item entities above Glow Item Frames for enhanced visibility

## Installation

1. Make sure you have Minecraft Forge 1.20.1 installed
2. Build the mod using `./gradlew build`
3. Copy the generated JAR file from `build/libs/` to your Minecraft `mods` folder
4. Launch Minecraft with Forge

## Building from Source

1. Clone or download this project
2. Open a terminal in the project directory
3. Run `./gradlew build` (or `gradlew.bat build` on Windows)
4. The built mod will be in `build/libs/casinomod-1.0.0.jar`

## Commands

### Basic Commands
- `/slotmachine` - Opens the casino slot machine interface
- `/slotmachine clear` - Manually clears items from your Glow Item Frames

### Item Management Commands
- `/slotmachine items list` - Shows all current slot machine items
- `/slotmachine items add` - Adds the item you're holding to the slot machine
- `/slotmachine items remove` - Removes the item you're holding from the slot machine
- `/slotmachine items reset` - Resets slot machine items to config defaults

### Display Options
- `/slotmachine floating` - Toggles large floating items above Glow Item Frames

## Blocks

- **Slot Machine Block** - Right-click to activate nearby Glow Item Frames
- **Casino Button** - Stylish button that activates slot machines when pressed

## Technical Details

- **Minecraft Version**: 1.20.1
- **Forge Version**: 47.4.0
- **Mod ID**: casinomod
- **Client/Server**: Works on both client and server

## Customization Ideas

You can easily customize the slot machine by editing the config file:

- **Rare Items Only**: Use only diamonds, emeralds, and netherite
- **Food Theme**: Use only food items like bread, apples, cooked meat
- **Ore Theme**: Use only ores and ingots
- **Modded Items**: Add items from other mods (if installed)

### 🎨 Glow Item Frame Setup Ideas

- **Casino Wall**: Mount 3 Glow Item Frames in a row on a wall for a classic slot machine look
- **Pedestal Display**: Place frames on item frames stands for a 3D casino experience
- **Themed Rooms**: Create different casino rooms with different item themes and frame setups
- **Multiplayer Casinos**: Set up multiple frame groups for different players

## Future Enhancements

Potential features that could be added:
- Betting system with in-game currency
- Different slot machine themes
- Sound effects
- Particle effects
- Multiple slot machine types
- Jackpot rewards

## License

All Rights Reserved

## Support

If you encounter any issues or have suggestions, please check the configuration file first to ensure items are properly formatted.

Enjoy your casino experience in Minecraft!
