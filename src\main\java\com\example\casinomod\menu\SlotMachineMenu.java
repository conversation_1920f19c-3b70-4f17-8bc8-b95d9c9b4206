package com.example.casinomod.menu;

import net.minecraft.network.FriendlyByteBuf;
import net.minecraft.world.entity.player.Inventory;
import net.minecraft.world.entity.player.Player;
import net.minecraft.world.inventory.AbstractContainerMenu;
import net.minecraft.world.inventory.Slot;
import net.minecraft.world.item.ItemStack;
import com.example.casinomod.CasinoMod;

public class SlotMachineMenu extends AbstractContainerMenu {

    public SlotMachineMenu(int windowId, Inventory playerInventory) {
        super(CasinoMod.SLOT_MACHINE_MENU.get(), windowId);
        setupSlots(playerInventory);
    }

    public SlotMachineMenu(int windowId, Inventory playerInventory, FriendlyByteBuf data) {
        super(CasinoMod.SLOT_MACHINE_MENU.get(), windowId);
        setupSlots(playerInventory);
    }

    private void setupSlots(Inventory playerInventory) {
        // Add player inventory slots
        for (int i = 0; i < 3; ++i) {
            for (int j = 0; j < 9; ++j) {
                this.addSlot(new Slot(playerInventory, j + i * 9 + 9, 8 + j * 18, 84 + i * 18));
            }
        }

        // Add player hotbar slots
        for (int k = 0; k < 9; ++k) {
            this.addSlot(new Slot(playerInventory, k, 8 + k * 18, 142));
        }
    }
    
    @Override
    public ItemStack quickMoveStack(Player player, int index) {
        return ItemStack.EMPTY;
    }
    
    @Override
    public boolean stillValid(Player player) {
        return true;
    }
}
