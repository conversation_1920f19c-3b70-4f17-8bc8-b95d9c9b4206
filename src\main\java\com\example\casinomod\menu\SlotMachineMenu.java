package com.example.casinomod.menu;

import net.minecraft.network.FriendlyByteBuf;
import net.minecraft.world.entity.player.Inventory;
import net.minecraft.world.entity.player.Player;
import net.minecraft.world.inventory.AbstractContainerMenu;

import net.minecraft.world.item.ItemStack;
import com.example.casinomod.CasinoMod;

public class SlotMachineMenu extends AbstractContainerMenu {

    public SlotMachineMenu(int windowId, Inventory playerInventory) {
        super(CasinoMod.SLOT_MACHINE_MENU.get(), windowId);
        // No slots needed for the casino interface - it's purely visual
    }

    public SlotMachineMenu(int windowId, Inventory playerInventory, FriendlyByteBuf data) {
        super(CasinoMod.SLOT_MACHINE_MENU.get(), windowId);
        // No slots needed for the casino interface - it's purely visual
    }
    
    @Override
    public ItemStack quickMoveStack(Player player, int index) {
        return ItemStack.EMPTY;
    }
    
    @Override
    public boolean stillValid(Player player) {
        return true;
    }
}
