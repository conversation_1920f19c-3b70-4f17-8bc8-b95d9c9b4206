package com.example.casinomod.server;

import com.example.casinomod.Config;
import com.example.casinomod.network.SlotMachinePackets;
import com.example.casinomod.util.ItemFrameSlotDisplay;
import com.example.casinomod.util.FloatingItemDisplay;

import net.minecraft.server.level.ServerPlayer;
import net.minecraft.world.entity.decoration.GlowItemFrame;
import net.minecraft.world.item.ItemStack;

import net.minecraftforge.event.TickEvent;
import net.minecraftforge.eventbus.api.SubscribeEvent;
import net.minecraftforge.fml.common.Mod;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Random;
import java.util.UUID;
import java.util.concurrent.ConcurrentHashMap;

@Mod.EventBusSubscriber(modid = "casinomod")
public class SlotMachineManager {
    
    private static final Map<UUID, SlotMachineSession> activeSessions = new ConcurrentHashMap<>();
    
    public static class SlotMachineSession {
        public final ServerPlayer player;
        public final List<GlowItemFrame> frames;
        public final List<ItemStack> availableItems;
        public final ItemStack[] finalResults;
        public boolean isSpinning;
        public int spinTimer;
        public int spinDuration;
        public int animationTick;
        
        public SlotMachineSession(ServerPlayer player, List<GlowItemFrame> frames) {
            this.player = player;
            this.frames = frames;
            this.availableItems = new ArrayList<>();
            this.finalResults = new ItemStack[3];
            this.isSpinning = false;
            this.spinTimer = 0;
            this.spinDuration = Config.spinDuration > 0 ? Config.spinDuration : 100;
            this.animationTick = 0;
            
            // Initialize available items from config
            initializeItems();
            
            // Set random final results
            Random random = new Random();
            for (int i = 0; i < 3; i++) {
                this.finalResults[i] = availableItems.get(random.nextInt(availableItems.size())).copy();
            }
        }
        
        private void initializeItems() {
            // Use the item manager to get current items
            availableItems.addAll(SlotMachineItemManager.getCurrentSlotItems());
        }
        
        public void startSpinning() {
            if (!isSpinning && ItemFrameSlotDisplay.validateFrames(frames)) {
                isSpinning = true;
                spinTimer = 0;
                animationTick = 0;

                // Generate new final results
                Random random = new Random();
                for (int i = 0; i < 3; i++) {
                    this.finalResults[i] = availableItems.get(random.nextInt(availableItems.size())).copy();
                }

                // Create floating items if enabled
                if (Config.enableFloatingItems && player.level() instanceof net.minecraft.server.level.ServerLevel serverLevel) {
                    FloatingItemDisplay.createFloatingItems(serverLevel, frames, player.getUUID());
                }

                player.sendSystemMessage(net.minecraft.network.chat.Component.literal("§6Slot machine connected to " + frames.size() + " Glow Item Frames!"));
            }
        }
        
        public void tick() {
            if (!isSpinning || !ItemFrameSlotDisplay.validateFrames(frames)) {
                return;
            }

            animationTick++;
            spinTimer++;

            // Update frames every 3 ticks for smooth animation
            if (spinTimer % 3 == 0) {
                updateFrames();
            }

            // Check if spinning should end
            if (spinTimer >= spinDuration) {
                endSpinning();
            }
        }
        
        private void updateFrames() {
            if (isSpinning) {
                // Show spinning items with faster animation
                ItemStack[] spinningItems = new ItemStack[3];
                for (int i = 0; i < 3; i++) {
                    // Create spinning effect by rapidly changing items
                    int itemIndex = (animationTick + i * 7) % availableItems.size();
                    spinningItems[i] = availableItems.get(itemIndex).copy();
                }
                ItemFrameSlotDisplay.updateSlotFrames(frames, spinningItems);

                // Update floating items if enabled
                if (Config.enableFloatingItems) {
                    FloatingItemDisplay.updateFloatingItems(player.getUUID(), spinningItems);
                }

                // Send sync packet to client every 6 ticks (3 times per second) for smooth sync
                if (spinTimer % 6 == 0) {
                    SlotMachinePackets.INSTANCE.send(net.minecraftforge.network.PacketDistributor.PLAYER.with(() -> player),
                        new SlotMachinePackets.SyncItemsPacket(spinningItems));
                }
            }
        }
        
        private void endSpinning() {
            isSpinning = false;

            // Show final results and keep them permanently
            ItemFrameSlotDisplay.updateSlotFrames(frames, finalResults);

            // Update floating items with final results
            if (Config.enableFloatingItems) {
                FloatingItemDisplay.updateFloatingItems(player.getUUID(), finalResults);
            }

            // Send final results to client for perfect sync
            SlotMachinePackets.INSTANCE.send(net.minecraftforge.network.PacketDistributor.PLAYER.with(() -> player),
                new SlotMachinePackets.SyncItemsPacket(finalResults));

            // Check for wins
            checkForWin();

            // Don't clear frames - keep the results displayed permanently
            String displayMessage = Config.enableFloatingItems ?
                "§aSlot machine results are displayed on Glow Item Frames and floating above them!" :
                "§aSlot machine results are now displayed on the Glow Item Frames!";
            player.sendSystemMessage(net.minecraft.network.chat.Component.literal(displayMessage));
        }
        
        private void checkForWin() {
            if (finalResults[0].getItem() == finalResults[1].getItem() && 
                finalResults[1].getItem() == finalResults[2].getItem()) {
                
                player.sendSystemMessage(net.minecraft.network.chat.Component.literal("§6JACKPOT! You won with " + 
                    finalResults[0].getHoverName().getString() + "!"));
            } else if (finalResults[0].getItem() == finalResults[1].getItem() || 
                       finalResults[1].getItem() == finalResults[2].getItem() || 
                       finalResults[0].getItem() == finalResults[2].getItem()) {
                
                player.sendSystemMessage(net.minecraft.network.chat.Component.literal("§eNice! Two matching items!"));
            } else {
                player.sendSystemMessage(net.minecraft.network.chat.Component.literal("§7Better luck next time!"));
            }
        }
        

        
        public ItemStack[] getCurrentDisplayItems() {
            if (isSpinning) {
                ItemStack[] spinningItems = new ItemStack[3];
                for (int i = 0; i < 3; i++) {
                    int itemIndex = (animationTick + i * 7) % availableItems.size();
                    spinningItems[i] = availableItems.get(itemIndex).copy();
                }
                return spinningItems;
            } else {
                return finalResults.clone();
            }
        }
    }
    
    public static void startSlotMachine(ServerPlayer player) {
        List<GlowItemFrame> frames = ItemFrameSlotDisplay.getAvailableSlotFrames(player);
        if (!frames.isEmpty()) {
            // Initialize item manager if needed
            SlotMachineItemManager.initializeFromConfig();

            SlotMachineSession session = new SlotMachineSession(player, frames);
            activeSessions.put(player.getUUID(), session);
            session.startSpinning();
        } else {
            player.sendSystemMessage(net.minecraft.network.chat.Component.literal("§cNo Glow Item Frames found within " + Config.itemFrameSearchRange + " blocks!"));
        }
    }
    
    public static void stopSlotMachine(ServerPlayer player) {
        SlotMachineSession session = activeSessions.get(player.getUUID());
        if (session != null) {
            // Don't remove the session if it's still spinning - let it continue
            if (!session.isSpinning) {
                activeSessions.remove(player.getUUID());
            }
            // Never clear frames when GUI is closed - keep results displayed
        }
    }
    
    public static SlotMachineSession getSession(ServerPlayer player) {
        return activeSessions.get(player.getUUID());
    }
    
    public static ItemStack[] getCurrentItems(ServerPlayer player) {
        SlotMachineSession session = getSession(player);
        return session != null ? session.getCurrentDisplayItems() : new ItemStack[]{ItemStack.EMPTY, ItemStack.EMPTY, ItemStack.EMPTY};
    }

    public static void clearPlayerFrames(ServerPlayer player) {
        SlotMachineSession session = activeSessions.remove(player.getUUID());
        if (session != null) {
            ItemFrameSlotDisplay.clearSlotFrames(session.frames);
            player.sendSystemMessage(net.minecraft.network.chat.Component.literal("§7Cleared slot machine frames."));
        }
    }
    
    @SubscribeEvent
    public static void onServerTick(TickEvent.ServerTickEvent event) {
        if (event.phase == TickEvent.Phase.END) {
            // Tick all active sessions
            activeSessions.values().forEach(SlotMachineSession::tick);
            
            // Remove sessions for disconnected players
            activeSessions.entrySet().removeIf(entry -> 
                entry.getValue().player == null || !entry.getValue().player.isAlive());
        }
    }
}
