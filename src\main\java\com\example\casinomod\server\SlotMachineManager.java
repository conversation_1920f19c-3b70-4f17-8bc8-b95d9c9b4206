package com.example.casinomod.server;

import com.example.casinomod.Config;
import com.example.casinomod.network.SlotMachinePackets;
import com.example.casinomod.util.ItemFrameSlotDisplay;
import com.example.casinomod.util.FloatingItemDisplay;
import com.example.casinomod.util.LargeItemDisplay;

import net.minecraft.server.level.ServerPlayer;
import net.minecraft.world.entity.decoration.GlowItemFrame;
import net.minecraft.world.item.ItemStack;

import net.minecraftforge.event.TickEvent;
import net.minecraftforge.event.entity.player.PlayerInteractEvent;
import net.minecraftforge.eventbus.api.SubscribeEvent;
import net.minecraftforge.fml.common.Mod;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Random;
import java.util.UUID;
import java.util.concurrent.ConcurrentHashMap;

@Mod.EventBusSubscriber(modid = "casinomod")
public class SlotMachineManager {
    
    private static final Map<UUID, SlotMachineSession> activeSessions = new ConcurrentHashMap<>();
    
    public static class SlotMachineSession {
        public final ServerPlayer player;
        public final List<GlowItemFrame> frames;
        public final List<ItemStack> availableItems;
        public final ItemStack[] finalResults;
        public boolean isSpinning;
        public int spinTimer;
        public int spinDuration;
        public int animationTick;
        
        public SlotMachineSession(ServerPlayer player, List<GlowItemFrame> frames) {
            this.player = player;
            this.frames = frames;
            this.availableItems = new ArrayList<>();
            this.finalResults = new ItemStack[3];
            this.isSpinning = false;
            this.spinTimer = 0;
            this.spinDuration = Config.spinDuration > 0 ? Config.spinDuration : 100;
            this.animationTick = 0;
            
            // Initialize available items from config
            initializeItems();
            
            // Set random final results
            Random random = new Random();
            for (int i = 0; i < 3; i++) {
                this.finalResults[i] = availableItems.get(random.nextInt(availableItems.size())).copy();
            }
        }
        
        private void initializeItems() {
            // Use the item manager to get current items
            availableItems.addAll(SlotMachineItemManager.getCurrentSlotItems());
        }
        
        public void startSpinning() {
            if (!isSpinning && ItemFrameSlotDisplay.validateFrames(frames)) {
                isSpinning = true;
                spinTimer = 0;
                animationTick = 0;

                // Generate new final results
                Random random = new Random();
                for (int i = 0; i < 3; i++) {
                    this.finalResults[i] = availableItems.get(random.nextInt(availableItems.size())).copy();
                }

                // Create enhanced item displays if enabled
                if (Config.enableFloatingItems && player.level() instanceof net.minecraft.server.level.ServerLevel serverLevel) {
                    if (Config.useLargeItemDisplay) {
                        // Use large armor stand displays for maximum visibility
                        System.out.println("Creating large armor stand displays for player: " + player.getName().getString());
                        LargeItemDisplay.createLargeItemDisplay(serverLevel, frames, player.getUUID());
                    } else {
                        // Use floating item entities
                        System.out.println("Creating floating item displays for player: " + player.getName().getString());
                        FloatingItemDisplay.createFloatingItems(serverLevel, frames, player.getUUID());
                    }
                }

                player.sendSystemMessage(net.minecraft.network.chat.Component.literal("§6Slot machine connected to " + frames.size() + " Glow Item Frames!"));
            }
        }
        
        public void tick() {
            if (!isSpinning || !ItemFrameSlotDisplay.validateFrames(frames)) {
                return;
            }

            animationTick++;
            spinTimer++;

            // Update frames every 3 ticks for smooth animation
            if (spinTimer % 3 == 0) {
                updateFrames();
            }

            // Check if spinning should end
            if (spinTimer >= spinDuration) {
                endSpinning();
            }
        }
        
        private void updateFrames() {
            if (isSpinning) {
                // Show spinning items with faster animation
                ItemStack[] spinningItems = new ItemStack[3];
                for (int i = 0; i < 3; i++) {
                    // Create spinning effect by rapidly changing items
                    int itemIndex = (animationTick + i * 7) % availableItems.size();
                    spinningItems[i] = availableItems.get(itemIndex).copy();
                }
                ItemFrameSlotDisplay.updateSlotFrames(frames, spinningItems);

                // Update enhanced item displays if enabled
                if (Config.enableFloatingItems) {
                    if (Config.useLargeItemDisplay) {
                        LargeItemDisplay.updateLargeItemDisplay(player.getUUID(), spinningItems);
                    } else {
                        FloatingItemDisplay.updateFloatingItems(player.getUUID(), spinningItems);
                    }
                }

                // Send sync packet to client every 6 ticks (3 times per second) for smooth sync
                if (spinTimer % 6 == 0) {
                    SlotMachinePackets.INSTANCE.send(net.minecraftforge.network.PacketDistributor.PLAYER.with(() -> player),
                        new SlotMachinePackets.SyncItemsPacket(spinningItems));
                }
            }
        }
        
        private void endSpinning() {
            isSpinning = false;

            // Show final results and keep them permanently
            ItemFrameSlotDisplay.updateSlotFrames(frames, finalResults);

            // Update enhanced item displays with final results
            if (Config.enableFloatingItems) {
                if (Config.useLargeItemDisplay) {
                    LargeItemDisplay.updateLargeItemDisplay(player.getUUID(), finalResults);
                } else {
                    FloatingItemDisplay.updateFloatingItems(player.getUUID(), finalResults);
                }
            }

            // Send final results to client for perfect sync
            SlotMachinePackets.INSTANCE.send(net.minecraftforge.network.PacketDistributor.PLAYER.with(() -> player),
                new SlotMachinePackets.SyncItemsPacket(finalResults));

            // Check for wins
            checkForWin();

            // Don't clear frames - keep the results displayed permanently
            String displayMessage;
            if (Config.enableFloatingItems) {
                if (Config.useLargeItemDisplay) {
                    displayMessage = "§aSlot machine results are displayed on Glow Item Frames with large displays above them!";
                } else {
                    displayMessage = "§aSlot machine results are displayed on Glow Item Frames and floating above them!";
                }
            } else {
                displayMessage = "§aSlot machine results are now displayed on the Glow Item Frames!";
            }
            player.sendSystemMessage(net.minecraft.network.chat.Component.literal(displayMessage));
        }
        
        private void checkForWin() {
            if (finalResults[0].getItem() == finalResults[1].getItem() && 
                finalResults[1].getItem() == finalResults[2].getItem()) {
                
                player.sendSystemMessage(net.minecraft.network.chat.Component.literal("§6JACKPOT! You won with " + 
                    finalResults[0].getHoverName().getString() + "!"));
            } else if (finalResults[0].getItem() == finalResults[1].getItem() || 
                       finalResults[1].getItem() == finalResults[2].getItem() || 
                       finalResults[0].getItem() == finalResults[2].getItem()) {
                
                player.sendSystemMessage(net.minecraft.network.chat.Component.literal("§eNice! Two matching items!"));
            } else {
                player.sendSystemMessage(net.minecraft.network.chat.Component.literal("§7Better luck next time!"));
            }
        }
        

        
        public ItemStack[] getCurrentDisplayItems() {
            if (isSpinning) {
                ItemStack[] spinningItems = new ItemStack[3];
                for (int i = 0; i < 3; i++) {
                    int itemIndex = (animationTick + i * 7) % availableItems.size();
                    spinningItems[i] = availableItems.get(itemIndex).copy();
                }
                return spinningItems;
            } else {
                return finalResults.clone();
            }
        }
    }
    
    public static void startSlotMachine(ServerPlayer player) {
        List<GlowItemFrame> frames = ItemFrameSlotDisplay.getAvailableSlotFrames(player);
        if (!frames.isEmpty()) {
            // Initialize item manager if needed
            SlotMachineItemManager.initializeFromConfig();

            SlotMachineSession session = new SlotMachineSession(player, frames);
            activeSessions.put(player.getUUID(), session);
            session.startSpinning();
        } else {
            player.sendSystemMessage(net.minecraft.network.chat.Component.literal("§cNo Glow Item Frames found within " + Config.itemFrameSearchRange + " blocks!"));
        }
    }
    
    public static void stopSlotMachine(ServerPlayer player) {
        SlotMachineSession session = activeSessions.get(player.getUUID());
        if (session != null) {
            // Don't remove the session if it's still spinning - let it continue
            if (!session.isSpinning) {
                activeSessions.remove(player.getUUID());
            }
            // Never clear frames when GUI is closed - keep results displayed
        }
    }
    
    public static SlotMachineSession getSession(ServerPlayer player) {
        return activeSessions.get(player.getUUID());
    }
    
    public static ItemStack[] getCurrentItems(ServerPlayer player) {
        SlotMachineSession session = getSession(player);
        return session != null ? session.getCurrentDisplayItems() : new ItemStack[]{ItemStack.EMPTY, ItemStack.EMPTY, ItemStack.EMPTY};
    }

    public static void clearPlayerFrames(ServerPlayer player) {
        SlotMachineSession session = activeSessions.remove(player.getUUID());
        if (session != null) {
            ItemFrameSlotDisplay.clearSlotFrames(session.frames);

            // Also clear enhanced item displays if they exist
            if (Config.enableFloatingItems && player.level() instanceof net.minecraft.server.level.ServerLevel serverLevel) {
                if (Config.useLargeItemDisplay) {
                    LargeItemDisplay.clearLargeItemDisplay(serverLevel, player.getUUID());
                } else {
                    FloatingItemDisplay.clearFloatingItems(serverLevel, player.getUUID());
                }
            }

            player.sendSystemMessage(net.minecraft.network.chat.Component.literal("§7Cleared slot machine frames and floating items."));
        }
    }
    
    @SubscribeEvent
    public static void onServerTick(TickEvent.ServerTickEvent event) {
        if (event.phase == TickEvent.Phase.END) {
            // Tick all active sessions
            activeSessions.values().forEach(SlotMachineSession::tick);

            // Remove sessions for disconnected players and clean up floating items
            activeSessions.entrySet().removeIf(entry -> {
                if (entry.getValue().player == null || !entry.getValue().player.isAlive()) {
                    // Clean up enhanced item displays for disconnected players
                    if (Config.enableFloatingItems && entry.getValue().player != null &&
                        entry.getValue().player.level() instanceof net.minecraft.server.level.ServerLevel serverLevel) {
                        if (Config.useLargeItemDisplay) {
                            LargeItemDisplay.clearLargeItemDisplay(serverLevel, entry.getKey());
                        } else {
                            FloatingItemDisplay.clearFloatingItems(serverLevel, entry.getKey());
                        }
                    }
                    return true;
                }
                return false;
            });
        }
    }

    @SubscribeEvent
    public static void onEntityInteract(PlayerInteractEvent.EntityInteract event) {
        // Check if a player is right-clicking a Glow Item Frame that's part of an active slot machine
        if (event.getEntity() instanceof ServerPlayer player &&
            event.getTarget() instanceof GlowItemFrame frame &&
            !frame.getItem().isEmpty()) {

            System.out.println("Player " + player.getName().getString() + " right-clicked a Glow Item Frame with item: " + frame.getItem().getHoverName().getString());
            handleItemCollection(player, frame);
        }
    }

    /**
     * Handles when a player right-clicks a Glow Item Frame to collect the item
     */
    private static void handleItemCollection(ServerPlayer player, GlowItemFrame frame) {
        SlotMachineSession session = activeSessions.get(player.getUUID());
        if (session == null || session.isSpinning) {
            System.out.println("No active session or still spinning for player: " + player.getName().getString());
            return; // No active session or still spinning
        }

        // Check if the frame is part of this player's slot machine
        int frameIndex = -1;
        for (int i = 0; i < session.frames.size(); i++) {
            if (session.frames.get(i) == frame) {
                frameIndex = i;
                break;
            }
        }

        if (frameIndex == -1) {
            System.out.println("Frame is not part of this slot machine for player: " + player.getName().getString());
            return; // Frame is not part of this slot machine
        }

        System.out.println("Player " + player.getName().getString() + " is collecting item from frame " + frameIndex);

        // Get the item from the frame
        ItemStack rewardItem = frame.getItem().copy();
        if (rewardItem.isEmpty()) {
            return; // No item to give
        }

        // Give the player the item
        if (!player.getInventory().add(rewardItem)) {
            // If inventory is full, drop the item
            player.drop(rewardItem, false);
        }

        // Remove the item from this frame only
        frame.setItem(ItemStack.EMPTY, false);

        // Clear items from the other frames (but keep the frames themselves)
        for (int i = 0; i < session.frames.size(); i++) {
            if (i != frameIndex) {
                GlowItemFrame otherFrame = session.frames.get(i);
                if (otherFrame != null && otherFrame.isAlive()) {
                    otherFrame.setItem(ItemStack.EMPTY, false);
                }
            }
        }

        // Clear enhanced item displays
        if (Config.enableFloatingItems && player.level() instanceof net.minecraft.server.level.ServerLevel serverLevel) {
            if (Config.useLargeItemDisplay) {
                LargeItemDisplay.clearLargeItemDisplay(serverLevel, player.getUUID());
            } else {
                FloatingItemDisplay.clearFloatingItems(serverLevel, player.getUUID());
            }
        }

        // Remove the session
        activeSessions.remove(player.getUUID());

        // Send message to player
        player.sendSystemMessage(net.minecraft.network.chat.Component.literal(
            "§6You collected " + rewardItem.getHoverName().getString() + " from the slot machine!"));
    }
}
