package com.example.casinomod.server;

import com.example.casinomod.Config;
import com.example.casinomod.util.ItemFrameSlotDisplay;
import net.minecraft.server.level.ServerPlayer;
import net.minecraft.world.entity.decoration.GlowItemFrame;
import net.minecraft.world.item.ItemStack;
import net.minecraft.world.item.Items;
import net.minecraftforge.event.TickEvent;
import net.minecraftforge.eventbus.api.SubscribeEvent;
import net.minecraftforge.fml.common.Mod;

import java.util.*;
import java.util.concurrent.ConcurrentHashMap;

@Mod.EventBusSubscriber(modid = "casinomod")
public class SlotMachineManager {
    
    private static final Map<UUID, SlotMachineSession> activeSessions = new ConcurrentHashMap<>();
    
    public static class SlotMachineSession {
        public final ServerPlayer player;
        public final List<GlowItemFrame> frames;
        public final List<ItemStack> availableItems;
        public final ItemStack[] finalResults;
        public boolean isSpinning;
        public int spinTimer;
        public int spinDuration;
        public int animationTick;
        
        public SlotMachineSession(ServerPlayer player, List<GlowItemFrame> frames) {
            this.player = player;
            this.frames = frames;
            this.availableItems = new ArrayList<>();
            this.finalResults = new ItemStack[3];
            this.isSpinning = false;
            this.spinTimer = 0;
            this.spinDuration = Config.spinDuration > 0 ? Config.spinDuration : 100;
            this.animationTick = 0;
            
            // Initialize available items from config
            initializeItems();
            
            // Set random final results
            Random random = new Random();
            for (int i = 0; i < 3; i++) {
                this.finalResults[i] = availableItems.get(random.nextInt(availableItems.size())).copy();
            }
        }
        
        private void initializeItems() {
            if (Config.slotMachineItems != null && !Config.slotMachineItems.isEmpty()) {
                Config.slotMachineItems.forEach(item -> availableItems.add(new ItemStack(item)));
            } else {
                // Fallback items
                availableItems.add(new ItemStack(Items.DIAMOND));
                availableItems.add(new ItemStack(Items.EMERALD));
                availableItems.add(new ItemStack(Items.GOLD_INGOT));
                availableItems.add(new ItemStack(Items.IRON_INGOT));
                availableItems.add(new ItemStack(Items.REDSTONE));
                availableItems.add(new ItemStack(Items.LAPIS_LAZULI));
                availableItems.add(new ItemStack(Items.COAL));
                availableItems.add(new ItemStack(Items.APPLE));
                availableItems.add(new ItemStack(Items.BREAD));
                availableItems.add(new ItemStack(Items.COOKED_BEEF));
            }
        }
        
        public void startSpinning() {
            if (!isSpinning && ItemFrameSlotDisplay.validateFrames(frames)) {
                isSpinning = true;
                spinTimer = 0;
                animationTick = 0;
                
                // Generate new final results
                Random random = new Random();
                for (int i = 0; i < 3; i++) {
                    this.finalResults[i] = availableItems.get(random.nextInt(availableItems.size())).copy();
                }
                
                player.sendSystemMessage(net.minecraft.network.chat.Component.literal("§6Slot machine connected to " + frames.size() + " Glow Item Frames!"));
            }
        }
        
        public void tick() {
            if (!isSpinning || !ItemFrameSlotDisplay.validateFrames(frames)) {
                return;
            }
            
            animationTick++;
            spinTimer++;
            
            // Update frames every 10 ticks instead of every 5 to reduce lag
            if (spinTimer % 10 == 0) {
                updateFrames();
            }
            
            // Check if spinning should end
            if (spinTimer >= spinDuration) {
                endSpinning();
            }
        }
        
        private void updateFrames() {
            if (isSpinning) {
                // Show spinning items
                ItemFrameSlotDisplay.updateSpinningFrames(frames, availableItems, animationTick);
            }
        }
        
        private void endSpinning() {
            isSpinning = false;
            
            // Show final results
            ItemFrameSlotDisplay.updateSlotFrames(frames, finalResults);
            
            // Check for wins
            checkForWin();
            
            // Schedule clearing frames after delay
            if (Config.clearFramesOnClose) {
                scheduleFrameClear();
            }
        }
        
        private void checkForWin() {
            if (finalResults[0].getItem() == finalResults[1].getItem() && 
                finalResults[1].getItem() == finalResults[2].getItem()) {
                
                player.sendSystemMessage(net.minecraft.network.chat.Component.literal("§6JACKPOT! You won with " + 
                    finalResults[0].getHoverName().getString() + "!"));
            } else if (finalResults[0].getItem() == finalResults[1].getItem() || 
                       finalResults[1].getItem() == finalResults[2].getItem() || 
                       finalResults[0].getItem() == finalResults[2].getItem()) {
                
                player.sendSystemMessage(net.minecraft.network.chat.Component.literal("§eNice! Two matching items!"));
            } else {
                player.sendSystemMessage(net.minecraft.network.chat.Component.literal("§7Better luck next time!"));
            }
        }
        
        private void scheduleFrameClear() {
            // Clear frames after 3 seconds (60 ticks)
            new Timer().schedule(new TimerTask() {
                @Override
                public void run() {
                    if (ItemFrameSlotDisplay.validateFrames(frames)) {
                        ItemFrameSlotDisplay.clearSlotFrames(frames);
                    }
                }
            }, 3000);
        }
        
        public ItemStack[] getCurrentDisplayItems() {
            if (isSpinning) {
                ItemStack[] spinningItems = new ItemStack[3];
                for (int i = 0; i < 3; i++) {
                    int itemIndex = (animationTick + i * 7) % availableItems.size();
                    spinningItems[i] = availableItems.get(itemIndex).copy();
                }
                return spinningItems;
            } else {
                return finalResults.clone();
            }
        }
    }
    
    public static void startSlotMachine(ServerPlayer player) {
        List<GlowItemFrame> frames = ItemFrameSlotDisplay.getAvailableSlotFrames(player);
        if (!frames.isEmpty()) {
            SlotMachineSession session = new SlotMachineSession(player, frames);
            activeSessions.put(player.getUUID(), session);
            session.startSpinning();
        }
    }
    
    public static void stopSlotMachine(ServerPlayer player) {
        SlotMachineSession session = activeSessions.remove(player.getUUID());
        if (session != null && Config.clearFramesOnClose) {
            ItemFrameSlotDisplay.clearSlotFrames(session.frames);
        }
    }
    
    public static SlotMachineSession getSession(ServerPlayer player) {
        return activeSessions.get(player.getUUID());
    }
    
    public static ItemStack[] getCurrentItems(ServerPlayer player) {
        SlotMachineSession session = getSession(player);
        return session != null ? session.getCurrentDisplayItems() : new ItemStack[]{ItemStack.EMPTY, ItemStack.EMPTY, ItemStack.EMPTY};
    }
    
    @SubscribeEvent
    public static void onServerTick(TickEvent.ServerTickEvent event) {
        if (event.phase == TickEvent.Phase.END) {
            // Tick all active sessions
            activeSessions.values().forEach(SlotMachineSession::tick);
            
            // Remove sessions for disconnected players
            activeSessions.entrySet().removeIf(entry -> 
                entry.getValue().player == null || !entry.getValue().player.isAlive());
        }
    }
}
