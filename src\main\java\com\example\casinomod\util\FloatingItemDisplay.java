package com.example.casinomod.util;

import net.minecraft.core.BlockPos;
import net.minecraft.server.level.ServerLevel;
import net.minecraft.world.entity.decoration.GlowItemFrame;
import net.minecraft.world.entity.item.ItemEntity;
import net.minecraft.world.item.ItemStack;
import net.minecraft.world.phys.Vec3;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.UUID;

public class FloatingItemDisplay {
    
    // Track floating items for each slot machine session
    private static final Map<UUID, List<ItemEntity>> floatingItems = new HashMap<>();
    
    /**
     * Creates floating item entities above the Glow Item Frames for larger display
     * @param level The server level
     * @param frames List of 3 Glow Item Frames
     * @param sessionId Unique session ID for tracking
     * @return List of created floating item entities
     */
    public static List<ItemEntity> createFloatingItems(ServerLevel level, List<GlowItemFrame> frames, UUID sessionId) {
        // Clear any existing floating items for this session
        clearFloatingItems(level, sessionId);

        List<ItemEntity> newFloatingItems = new ArrayList<>();

        for (int i = 0; i < Math.min(frames.size(), 3); i++) {
            GlowItemFrame frame = frames.get(i);
            if (frame != null && frame.isAlive()) {
                // Create floating item entity above the frame
                Vec3 framePos = frame.position();
                Vec3 floatingPos = framePos.add(0, 1.5, 0); // 1.5 blocks above the frame

                // Start with an empty item that will be updated during spinning
                ItemEntity floatingItem = new ItemEntity(level, floatingPos.x, floatingPos.y, floatingPos.z, ItemStack.EMPTY);

                // Configure the floating item for visibility
                floatingItem.setNoGravity(true);
                floatingItem.setNeverPickUp();
                floatingItem.setUnlimitedLifetime();
                floatingItem.setDeltaMovement(Vec3.ZERO); // No movement
                floatingItem.setGlowingTag(true); // Make it glow for visibility

                // Set a longer lifespan
                floatingItem.lifespan = Integer.MAX_VALUE;

                // Add to world
                level.addFreshEntity(floatingItem);
                newFloatingItems.add(floatingItem);
            }
        }

        // Store the floating items for this session
        floatingItems.put(sessionId, newFloatingItems);
        return newFloatingItems;
    }
    
    /**
     * Updates the items displayed by the floating entities
     * @param sessionId Session ID to update
     * @param items Array of 3 ItemStacks to display
     */
    public static void updateFloatingItems(UUID sessionId, ItemStack[] items) {
        List<ItemEntity> entities = floatingItems.get(sessionId);
        if (entities == null || items.length != 3) {
            return;
        }
        
        for (int i = 0; i < Math.min(entities.size(), 3); i++) {
            ItemEntity entity = entities.get(i);
            if (entity != null && entity.isAlive() && i < items.length) {
                entity.setItem(items[i].copy());
            }
        }
    }
    
    /**
     * Clears floating items for a specific session
     * @param level The server level
     * @param sessionId Session ID to clear
     */
    public static void clearFloatingItems(ServerLevel level, UUID sessionId) {
        List<ItemEntity> entities = floatingItems.remove(sessionId);
        if (entities != null) {
            for (ItemEntity entity : entities) {
                if (entity != null && entity.isAlive()) {
                    entity.discard();
                }
            }
        }
    }
    
    /**
     * Clears all floating items (useful for cleanup)
     * @param level The server level
     */
    public static void clearAllFloatingItems(ServerLevel level) {
        for (UUID sessionId : new ArrayList<>(floatingItems.keySet())) {
            clearFloatingItems(level, sessionId);
        }
    }
    
    /**
     * Checks if floating items are active for a session
     * @param sessionId Session ID to check
     * @return true if floating items exist and are valid
     */
    public static boolean hasFloatingItems(UUID sessionId) {
        List<ItemEntity> entities = floatingItems.get(sessionId);
        if (entities == null || entities.isEmpty()) {
            return false;
        }
        
        // Check if any entities are still alive
        for (ItemEntity entity : entities) {
            if (entity != null && entity.isAlive()) {
                return true;
            }
        }
        
        // All entities are dead, remove from tracking
        floatingItems.remove(sessionId);
        return false;
    }
    
    /**
     * Creates spinning effect for floating items
     * @param sessionId Session ID
     * @param availableItems List of available items
     * @param animationTick Current animation tick
     */
    public static void updateSpinningFloatingItems(UUID sessionId, List<ItemStack> availableItems, int animationTick) {
        if (availableItems.isEmpty()) {
            return;
        }
        
        ItemStack[] spinningItems = new ItemStack[3];
        for (int i = 0; i < 3; i++) {
            int itemIndex = (animationTick + i * 7) % availableItems.size();
            spinningItems[i] = availableItems.get(itemIndex).copy();
        }
        
        updateFloatingItems(sessionId, spinningItems);
    }
}
