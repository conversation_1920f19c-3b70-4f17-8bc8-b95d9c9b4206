package com.example.casinomod.server;

import com.example.casinomod.Config;
import net.minecraft.network.chat.Component;
import net.minecraft.resources.ResourceLocation;
import net.minecraft.server.level.ServerPlayer;
import net.minecraft.world.item.Item;
import net.minecraft.world.item.ItemStack;
import net.minecraftforge.registries.ForgeRegistries;

import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Set;

public class SlotMachineItemManager {
    
    // Runtime storage for slot machine items (per-server session)
    private static Set<Item> runtimeSlotItems = new HashSet<>();
    private static boolean useRuntimeItems = false;
    
    /**
     * Initialize runtime items from config
     */
    public static void initializeFromConfig() {
        runtimeSlotItems.clear();
        if (Config.slotMachineItems != null) {
            runtimeSlotItems.addAll(Config.slotMachineItems);
        }
        useRuntimeItems = false;
    }
    
    /**
     * Get the death minigame items (fixed set for the death casino)
     */
    public static List<ItemStack> getCurrentSlotItems() {
        List<ItemStack> items = new ArrayList<>();

        // Fixed death minigame items
        items.add(new ItemStack(net.minecraft.world.item.Items.ENDER_PEARL));
        items.add(new ItemStack(net.minecraft.world.item.Items.TNT));
        items.add(new ItemStack(net.minecraft.world.item.Items.GOLDEN_APPLE));

        return items;
    }
    
    /**
     * Add an item to the slot machine
     */
    public static boolean addItem(ServerPlayer player, Item item) {
        if (item == null) {
            player.sendSystemMessage(Component.literal("§cInvalid item!"));
            return false;
        }
        
        // Switch to runtime mode if not already
        if (!useRuntimeItems) {
            initializeFromConfig();
            useRuntimeItems = true;
        }
        
        if (runtimeSlotItems.contains(item)) {
            player.sendSystemMessage(Component.literal("§eItem " + getItemName(item) + " is already in the slot machine!"));
            return false;
        }
        
        runtimeSlotItems.add(item);
        player.sendSystemMessage(Component.literal("§aAdded " + getItemName(item) + " to the slot machine! (" + runtimeSlotItems.size() + " items total)"));
        return true;
    }
    
    /**
     * Remove an item from the slot machine
     */
    public static boolean removeItem(ServerPlayer player, Item item) {
        if (item == null) {
            player.sendSystemMessage(Component.literal("§cInvalid item!"));
            return false;
        }
        
        // Switch to runtime mode if not already
        if (!useRuntimeItems) {
            initializeFromConfig();
            useRuntimeItems = true;
        }
        
        if (!runtimeSlotItems.contains(item)) {
            player.sendSystemMessage(Component.literal("§eItem " + getItemName(item) + " is not in the slot machine!"));
            return false;
        }
        
        if (runtimeSlotItems.size() <= 1) {
            player.sendSystemMessage(Component.literal("§cCannot remove the last item! Slot machine needs at least one item."));
            return false;
        }
        
        runtimeSlotItems.remove(item);
        player.sendSystemMessage(Component.literal("§aRemoved " + getItemName(item) + " from the slot machine! (" + runtimeSlotItems.size() + " items remaining)"));
        return true;
    }
    
    /**
     * List all current slot machine items
     */
    public static void listItems(ServerPlayer player) {
        Set<Item> itemsToShow = useRuntimeItems ? runtimeSlotItems : Config.slotMachineItems;
        
        if (itemsToShow == null || itemsToShow.isEmpty()) {
            player.sendSystemMessage(Component.literal("§eSlot machine is using default items (diamond, emerald, gold, iron)"));
            return;
        }
        
        player.sendSystemMessage(Component.literal("§6=== Slot Machine Items (" + itemsToShow.size() + ") ==="));
        int index = 1;
        for (Item item : itemsToShow) {
            player.sendSystemMessage(Component.literal("§7" + index + ". §f" + getItemName(item)));
            index++;
        }
        
        if (useRuntimeItems) {
            player.sendSystemMessage(Component.literal("§7(Using runtime configuration - changes will reset on server restart)"));
        } else {
            player.sendSystemMessage(Component.literal("§7(Using config file - edit config for permanent changes)"));
        }
    }
    
    /**
     * Add the item the player is currently holding
     */
    public static boolean addHeldItem(ServerPlayer player) {
        ItemStack heldItem = player.getMainHandItem();
        if (heldItem.isEmpty()) {
            player.sendSystemMessage(Component.literal("§cYou must be holding an item to add it to the slot machine!"));
            return false;
        }
        
        return addItem(player, heldItem.getItem());
    }
    
    /**
     * Remove the item the player is currently holding
     */
    public static boolean removeHeldItem(ServerPlayer player) {
        ItemStack heldItem = player.getMainHandItem();
        if (heldItem.isEmpty()) {
            player.sendSystemMessage(Component.literal("§cYou must be holding an item to remove it from the slot machine!"));
            return false;
        }
        
        return removeItem(player, heldItem.getItem());
    }
    
    /**
     * Reset to config defaults
     */
    public static void resetToConfig(ServerPlayer player) {
        useRuntimeItems = false;
        runtimeSlotItems.clear();
        player.sendSystemMessage(Component.literal("§aSlot machine items reset to config defaults!"));
    }
    
    /**
     * Get a user-friendly name for an item
     */
    private static String getItemName(Item item) {
        ResourceLocation itemId = ForgeRegistries.ITEMS.getKey(item);
        return itemId != null ? itemId.toString() : "Unknown Item";
    }
}
