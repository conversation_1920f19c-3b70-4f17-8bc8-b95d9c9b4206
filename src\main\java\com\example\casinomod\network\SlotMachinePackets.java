package com.example.casinomod.network;

import com.example.casinomod.CasinoMod;
import com.example.casinomod.server.SlotMachineManager;
import net.minecraft.network.FriendlyByteBuf;
import net.minecraft.resources.ResourceLocation;
import net.minecraft.server.level.ServerPlayer;
import net.minecraft.world.item.ItemStack;
import net.minecraftforge.network.NetworkEvent;
import net.minecraftforge.network.NetworkRegistry;
import net.minecraftforge.network.simple.SimpleChannel;

import java.util.function.Supplier;

public class SlotMachinePackets {
    
    private static final String PROTOCOL_VERSION = "1";
    public static final SimpleChannel INSTANCE = NetworkRegistry.newSimpleChannel(
        ResourceLocation.fromNamespaceAndPath(CasinoMod.MODID, "main"),
        () -> PROTOCOL_VERSION,
        PROTOCOL_VERSION::equals,
        PROTOCOL_VERSION::equals
    );
    
    public static void register() {
        int id = 0;
        INSTANCE.registerMessage(id++, SpinStartPacket.class, SpinStartPacket::encode, SpinStartPacket::decode, SpinStartPacket::handle);
        INSTANCE.registerMessage(id++, SpinStopPacket.class, SpinStopPacket::encode, SpinStopPacket::decode, SpinStopPacket::handle);
        INSTANCE.registerMessage(id++, SyncItemsPacket.class, SyncItemsPacket::encode, SyncItemsPacket::decode, SyncItemsPacket::handle);
    }
    
    // Packet sent when spinning starts
    public static class SpinStartPacket {

        public SpinStartPacket() {}

        public static void encode(SpinStartPacket packet, FriendlyByteBuf buffer) {
            // No data needed for start packet
        }

        public static SpinStartPacket decode(FriendlyByteBuf buffer) {
            return new SpinStartPacket();
        }

        public static void handle(SpinStartPacket packet, Supplier<NetworkEvent.Context> contextSupplier) {
            NetworkEvent.Context context = contextSupplier.get();
            context.enqueueWork(() -> {
                ServerPlayer player = context.getSender();
                if (player != null) {
                    // Start the slot machine on the server
                    SlotMachineManager.startSlotMachine(player);
                }
            });
            context.setPacketHandled(true);
        }
    }
    
    // Packet sent when spinning stops
    public static class SpinStopPacket {

        public SpinStopPacket() {}

        public static void encode(SpinStopPacket packet, FriendlyByteBuf buffer) {
            // No data needed for stop packet
        }

        public static SpinStopPacket decode(FriendlyByteBuf buffer) {
            return new SpinStopPacket();
        }

        public static void handle(SpinStopPacket packet, Supplier<NetworkEvent.Context> contextSupplier) {
            NetworkEvent.Context context = contextSupplier.get();
            context.enqueueWork(() -> {
                ServerPlayer player = context.getSender();
                if (player != null) {
                    // Stop the slot machine on the server
                    SlotMachineManager.stopSlotMachine(player);
                }
            });
            context.setPacketHandled(true);
        }
    }
    
    // Packet sent from server to client to sync current slot items
    public static class SyncItemsPacket {
        private final ItemStack[] items;

        public SyncItemsPacket(ItemStack[] items) {
            this.items = items;
        }

        public static void encode(SyncItemsPacket packet, FriendlyByteBuf buffer) {
            buffer.writeInt(packet.items.length);
            for (ItemStack item : packet.items) {
                buffer.writeItem(item);
            }
        }

        public static SyncItemsPacket decode(FriendlyByteBuf buffer) {
            int length = buffer.readInt();
            ItemStack[] items = new ItemStack[length];
            for (int i = 0; i < length; i++) {
                items[i] = buffer.readItem();
            }
            return new SyncItemsPacket(items);
        }

        public static void handle(SyncItemsPacket packet, Supplier<NetworkEvent.Context> contextSupplier) {
            NetworkEvent.Context context = contextSupplier.get();
            context.enqueueWork(() -> {
                // Update the client-side slot machine screen with synced items
                net.minecraft.client.Minecraft minecraft = net.minecraft.client.Minecraft.getInstance();
                if (minecraft.screen instanceof com.example.casinomod.client.screen.SlotMachineScreen slotScreen) {
                    slotScreen.updateSyncedItems(packet.items);
                }
            });
            context.setPacketHandled(true);
        }
    }
}
