package com.example.casinomod.network;

import com.example.casinomod.CasinoMod;
import com.example.casinomod.util.ItemFrameSlotDisplay;
import net.minecraft.network.FriendlyByteBuf;
import net.minecraft.resources.ResourceLocation;
import net.minecraft.server.level.ServerPlayer;
import net.minecraft.world.entity.decoration.GlowItemFrame;
import net.minecraft.world.item.ItemStack;
import net.minecraftforge.network.NetworkEvent;
import net.minecraftforge.network.NetworkRegistry;
import net.minecraftforge.network.simple.SimpleChannel;

import java.util.List;
import java.util.function.Supplier;

public class SlotMachinePackets {
    
    private static final String PROTOCOL_VERSION = "1";
    public static final SimpleChannel INSTANCE = NetworkRegistry.newSimpleChannel(
        ResourceLocation.fromNamespaceAndPath(CasinoMod.MODID, "main"),
        () -> PROTOCOL_VERSION,
        PROTOCOL_VERSION::equals,
        PROTOCOL_VERSION::equals
    );
    
    public static void register() {
        int id = 0;
        INSTANCE.registerMessage(id++, SpinStartPacket.class, SpinStartPacket::encode, SpinStartPacket::decode, SpinStartPacket::handle);
        INSTANCE.registerMessage(id++, SpinUpdatePacket.class, SpinUpdatePacket::encode, SpinUpdatePacket::decode, SpinUpdatePacket::handle);
        INSTANCE.registerMessage(id++, SpinEndPacket.class, SpinEndPacket::encode, SpinEndPacket::decode, SpinEndPacket::handle);
    }
    
    // Packet sent when spinning starts
    public static class SpinStartPacket {
        
        public SpinStartPacket() {}
        
        public static void encode(SpinStartPacket packet, FriendlyByteBuf buffer) {
            // No data needed for start packet
        }
        
        public static SpinStartPacket decode(FriendlyByteBuf buffer) {
            return new SpinStartPacket();
        }
        
        public static void handle(SpinStartPacket packet, Supplier<NetworkEvent.Context> contextSupplier) {
            NetworkEvent.Context context = contextSupplier.get();
            context.enqueueWork(() -> {
                ServerPlayer player = context.getSender();
                if (player != null) {
                    // Find available item frames for this player
                    List<GlowItemFrame> frames = ItemFrameSlotDisplay.getAvailableSlotFrames(player);
                    if (!frames.isEmpty()) {
                        // Store the frames for this player (you might want to use a more sophisticated storage)
                        player.getPersistentData().putBoolean("casino_using_frames", true);
                        
                        // Send confirmation back to client if needed
                        player.sendSystemMessage(net.minecraft.network.chat.Component.literal("§6Slot machine connected to " + frames.size() + " Glow Item Frames!"));
                    }
                }
            });
            context.setPacketHandled(true);
        }
    }
    
    // Packet sent during spinning to update item frames
    public static class SpinUpdatePacket {
        private final ItemStack[] items;
        
        public SpinUpdatePacket(ItemStack[] items) {
            this.items = items;
        }
        
        public static void encode(SpinUpdatePacket packet, FriendlyByteBuf buffer) {
            buffer.writeInt(packet.items.length);
            for (ItemStack item : packet.items) {
                buffer.writeItem(item);
            }
        }
        
        public static SpinUpdatePacket decode(FriendlyByteBuf buffer) {
            int length = buffer.readInt();
            ItemStack[] items = new ItemStack[length];
            for (int i = 0; i < length; i++) {
                items[i] = buffer.readItem();
            }
            return new SpinUpdatePacket(items);
        }
        
        public static void handle(SpinUpdatePacket packet, Supplier<NetworkEvent.Context> contextSupplier) {
            NetworkEvent.Context context = contextSupplier.get();
            context.enqueueWork(() -> {
                ServerPlayer player = context.getSender();
                if (player != null && player.getPersistentData().getBoolean("casino_using_frames")) {
                    List<GlowItemFrame> frames = ItemFrameSlotDisplay.getAvailableSlotFrames(player);
                    if (ItemFrameSlotDisplay.validateFrames(frames)) {
                        ItemFrameSlotDisplay.updateSlotFrames(frames, packet.items);
                    }
                }
            });
            context.setPacketHandled(true);
        }
    }
    
    // Packet sent when spinning ends with final results
    public static class SpinEndPacket {
        private final ItemStack[] finalItems;
        private final boolean clearFrames;
        
        public SpinEndPacket(ItemStack[] finalItems, boolean clearFrames) {
            this.finalItems = finalItems;
            this.clearFrames = clearFrames;
        }
        
        public static void encode(SpinEndPacket packet, FriendlyByteBuf buffer) {
            buffer.writeInt(packet.finalItems.length);
            for (ItemStack item : packet.finalItems) {
                buffer.writeItem(item);
            }
            buffer.writeBoolean(packet.clearFrames);
        }
        
        public static SpinEndPacket decode(FriendlyByteBuf buffer) {
            int length = buffer.readInt();
            ItemStack[] items = new ItemStack[length];
            for (int i = 0; i < length; i++) {
                items[i] = buffer.readItem();
            }
            boolean clearFrames = buffer.readBoolean();
            return new SpinEndPacket(items, clearFrames);
        }
        
        public static void handle(SpinEndPacket packet, Supplier<NetworkEvent.Context> contextSupplier) {
            NetworkEvent.Context context = contextSupplier.get();
            context.enqueueWork(() -> {
                ServerPlayer player = context.getSender();
                if (player != null && player.getPersistentData().getBoolean("casino_using_frames")) {
                    List<GlowItemFrame> frames = ItemFrameSlotDisplay.getAvailableSlotFrames(player);
                    if (ItemFrameSlotDisplay.validateFrames(frames)) {
                        if (packet.clearFrames) {
                            // Clear frames after a delay
                            player.getServer().execute(() -> {
                                try {
                                    Thread.sleep(3000); // Show results for 3 seconds
                                    ItemFrameSlotDisplay.clearSlotFrames(frames);
                                } catch (InterruptedException e) {
                                    Thread.currentThread().interrupt();
                                }
                            });
                        } else {
                            // Show final results
                            ItemFrameSlotDisplay.updateSlotFrames(frames, packet.finalItems);
                        }
                    }
                    player.getPersistentData().putBoolean("casino_using_frames", false);
                }
            });
            context.setPacketHandled(true);
        }
    }
}
