package com.example.casinomod.blocks;

import com.example.casinomod.server.SlotMachineManager;
import net.minecraft.core.BlockPos;
import net.minecraft.server.level.ServerPlayer;
import net.minecraft.world.InteractionHand;
import net.minecraft.world.InteractionResult;
import net.minecraft.world.entity.player.Player;
import net.minecraft.world.level.Level;
import net.minecraft.world.level.block.Block;
import net.minecraft.world.level.block.state.BlockState;
import net.minecraft.world.phys.BlockHitResult;

public class SlotMachineBlock extends Block {
    
    public SlotMachineBlock(Properties properties) {
        super(properties);
    }
    
    @Override
    public InteractionResult use(BlockState state, Level level, BlockPos pos, Player player, InteractionHand hand, BlockHitResult hit) {
        if (!level.isClientSide && player instanceof ServerPlayer serverPlayer) {
            // Start the slot machine for this player
            SlotMachineManager.startSlotMachine(serverPlayer);
            player.sendSystemMessage(net.minecraft.network.chat.Component.literal("§6Slot Machine activated! Check nearby Glow Item Frames!"));
            return InteractionResult.SUCCESS;
        }
        return InteractionResult.sidedSuccess(level.isClientSide);
    }
}
