package com.example.casinomod.blocks;

import com.example.casinomod.server.SlotMachineManager;
import net.minecraft.core.BlockPos;
import net.minecraft.server.level.ServerPlayer;
import net.minecraft.sounds.SoundEvents;
import net.minecraft.sounds.SoundSource;
import net.minecraft.world.InteractionHand;
import net.minecraft.world.InteractionResult;
import net.minecraft.world.entity.player.Player;
import net.minecraft.world.level.Level;
import net.minecraft.world.level.block.Block;
import net.minecraft.world.level.block.state.BlockState;
import net.minecraft.world.level.block.state.StateDefinition;
import net.minecraft.world.level.block.state.properties.BooleanProperty;
import net.minecraft.world.phys.BlockHitResult;

public class CasinoButtonBlock extends Block {
    
    public static final BooleanProperty POWERED = BooleanProperty.create("powered");
    
    public CasinoButtonBlock(Properties properties) {
        super(properties);
        this.registerDefaultState(this.stateDefinition.any().setValue(POWERED, false));
    }
    
    @Override
    protected void createBlockStateDefinition(StateDefinition.Builder<Block, BlockState> builder) {
        builder.add(POWERED);
    }
    
    @Override
    public InteractionResult use(BlockState state, Level level, BlockPos pos, Player player, InteractionHand hand, BlockHitResult hit) {
        if (!level.isClientSide && player instanceof ServerPlayer serverPlayer) {
            // Toggle the button state
            boolean isPowered = state.getValue(POWERED);
            level.setBlock(pos, state.setValue(POWERED, !isPowered), 3);
            
            // Play button sound
            level.playSound(null, pos, SoundEvents.STONE_BUTTON_CLICK_ON, SoundSource.BLOCKS, 0.3F, isPowered ? 0.5F : 0.6F);
            
            if (!isPowered) {
                // Button was pressed - start slot machine
                SlotMachineManager.startSlotMachine(serverPlayer);
                player.sendSystemMessage(net.minecraft.network.chat.Component.literal("§6Casino Button activated! Slot machine spinning!"));
                
                // Schedule button to turn off after 2 seconds
                level.scheduleTick(pos, this, 40);
            }
            
            return InteractionResult.SUCCESS;
        }
        return InteractionResult.sidedSuccess(level.isClientSide);
    }
    
    @Override
    public void tick(BlockState state, net.minecraft.server.level.ServerLevel level, BlockPos pos, net.minecraft.util.RandomSource random) {
        if (state.getValue(POWERED)) {
            level.setBlock(pos, state.setValue(POWERED, false), 3);
            level.playSound(null, pos, SoundEvents.STONE_BUTTON_CLICK_OFF, SoundSource.BLOCKS, 0.3F, 0.5F);
        }
    }
}
