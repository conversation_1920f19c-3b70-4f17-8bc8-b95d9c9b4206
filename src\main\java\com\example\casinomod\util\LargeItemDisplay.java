package com.example.casinomod.util;

import net.minecraft.server.level.ServerLevel;
import net.minecraft.world.entity.decoration.ArmorStand;
import net.minecraft.world.entity.decoration.GlowItemFrame;
import net.minecraft.world.item.ItemStack;
import net.minecraft.world.phys.Vec3;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.UUID;

public class LargeItemDisplay {
    
    // Track armor stands for each slot machine session
    private static final Map<UUID, List<ArmorStand>> displayStands = new HashMap<>();
    
    /**
     * Creates large item displays using armor stands above the Glow Item Frames
     * @param level The server level
     * @param frames List of 3 Glow Item Frames
     * @param sessionId Unique session ID for tracking
     * @return List of created armor stands
     */
    public static List<ArmorStand> createLargeItemDisplay(ServerLevel level, List<GlowItemFrame> frames, UUID sessionId) {
        // Clear any existing displays for this session
        clearLargeItemDisplay(level, sessionId);
        
        List<ArmorStand> newDisplayStands = new ArrayList<>();
        
        for (int i = 0; i < Math.min(frames.size(), 3); i++) {
            GlowItemFrame frame = frames.get(i);
            if (frame != null && frame.isAlive()) {
                // Create armor stand above the frame
                Vec3 framePos = frame.position();
                Vec3 standPos = framePos.add(0, 2.0, 0); // 2 blocks above the frame
                
                ArmorStand armorStand = new ArmorStand(level, standPos.x, standPos.y, standPos.z);
                
                // Configure the armor stand for large item display
                armorStand.setInvisible(true); // Make the armor stand invisible
                armorStand.setNoGravity(true);
                // Note: setMarker and setSmall are private in 1.20.1, so we'll skip those
                armorStand.setNoBasePlate(true);
                armorStand.setShowArms(true); // Show arms to hold items
                armorStand.setGlowingTag(true); // Make it glow
                
                // Set initial item (will be updated during spinning)
                ItemStack initialItem = new ItemStack(net.minecraft.world.item.Items.DIAMOND);
                armorStand.setItemInHand(net.minecraft.world.InteractionHand.MAIN_HAND, initialItem);
                
                // Add to world
                level.addFreshEntity(armorStand);
                newDisplayStands.add(armorStand);
            }
        }
        
        // Store the armor stands for this session
        displayStands.put(sessionId, newDisplayStands);
        return newDisplayStands;
    }
    
    /**
     * Updates the items displayed by the armor stands
     * @param sessionId Session ID to update
     * @param items Array of 3 ItemStacks to display
     */
    public static void updateLargeItemDisplay(UUID sessionId, ItemStack[] items) {
        List<ArmorStand> stands = displayStands.get(sessionId);
        if (stands == null || items.length != 3) {
            return;
        }
        
        for (int i = 0; i < Math.min(stands.size(), 3); i++) {
            ArmorStand stand = stands.get(i);
            if (stand != null && stand.isAlive() && i < items.length) {
                ItemStack itemToSet = items[i].isEmpty() ? new ItemStack(net.minecraft.world.item.Items.BARRIER) : items[i].copy();
                stand.setItemInHand(net.minecraft.world.InteractionHand.MAIN_HAND, itemToSet);
            }
        }
    }
    
    /**
     * Clears large item displays for a specific session
     * @param level The server level
     * @param sessionId Session ID to clear
     */
    public static void clearLargeItemDisplay(ServerLevel level, UUID sessionId) {
        List<ArmorStand> stands = displayStands.remove(sessionId);
        if (stands != null) {
            for (ArmorStand stand : stands) {
                if (stand != null && stand.isAlive()) {
                    stand.discard();
                }
            }
        }
    }
    
    /**
     * Clears all large item displays (useful for cleanup)
     * @param level The server level
     */
    public static void clearAllLargeItemDisplays(ServerLevel level) {
        for (UUID sessionId : new ArrayList<>(displayStands.keySet())) {
            clearLargeItemDisplay(level, sessionId);
        }
    }
    
    /**
     * Checks if large item displays are active for a session
     * @param sessionId Session ID to check
     * @return true if displays exist and are valid
     */
    public static boolean hasLargeItemDisplay(UUID sessionId) {
        List<ArmorStand> stands = displayStands.get(sessionId);
        if (stands == null || stands.isEmpty()) {
            return false;
        }
        
        // Check if any stands are still alive
        for (ArmorStand stand : stands) {
            if (stand != null && stand.isAlive()) {
                return true;
            }
        }
        
        // All stands are dead, remove from tracking
        displayStands.remove(sessionId);
        return false;
    }
    
    /**
     * Creates spinning effect for large item displays
     * @param sessionId Session ID
     * @param availableItems List of available items
     * @param animationTick Current animation tick
     */
    public static void updateSpinningLargeDisplay(UUID sessionId, List<ItemStack> availableItems, int animationTick) {
        if (availableItems.isEmpty()) {
            return;
        }
        
        ItemStack[] spinningItems = new ItemStack[3];
        for (int i = 0; i < 3; i++) {
            int itemIndex = (animationTick + i * 7) % availableItems.size();
            spinningItems[i] = availableItems.get(itemIndex).copy();
        }
        
        updateLargeItemDisplay(sessionId, spinningItems);
    }
}
