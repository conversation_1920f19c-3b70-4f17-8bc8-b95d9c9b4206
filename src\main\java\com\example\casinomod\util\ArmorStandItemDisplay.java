package com.example.casinomod.util;

import net.minecraft.core.BlockPos;
import net.minecraft.server.level.ServerLevel;
import net.minecraft.world.entity.decoration.ArmorStand;
import net.minecraft.world.entity.decoration.GlowItemFrame;
import net.minecraft.world.item.ItemStack;
import net.minecraft.world.phys.Vec3;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.UUID;

public class ArmorStandItemDisplay {
    
    // Track armor stands for each slot machine session
    private static final Map<UUID, List<ArmorStand>> armorStands = new HashMap<>();
    
    /**
     * Creates armor stands above the Glow Item Frames for larger item display
     * @param level The server level
     * @param frames List of 3 Glow Item Frames
     * @param sessionId Unique session ID for tracking
     * @return List of created armor stands
     */
    public static List<ArmorStand> createArmorStandDisplay(ServerLevel level, List<GlowItemFrame> frames, UUID sessionId) {
        // Clear any existing armor stands for this session
        clearArmorStands(level, sessionId);
        
        List<ArmorStand> newArmorStands = new ArrayList<>();
        
        System.out.println("Creating armor stand displays for " + frames.size() + " frames");
        
        for (int i = 0; i < Math.min(frames.size(), 3); i++) {
            GlowItemFrame frame = frames.get(i);
            if (frame != null && frame.isAlive()) {
                // Create armor stand above the frame
                Vec3 framePos = frame.position();
                Vec3 standPos = framePos.add(0, 1.0, 0); // 1 block above the frame
                
                System.out.println("Creating armor stand at position: " + standPos);
                
                ArmorStand armorStand = new ArmorStand(level, standPos.x, standPos.y, standPos.z);
                
                // Configure the armor stand for display
                armorStand.setInvisible(true); // Make the armor stand invisible
                armorStand.setNoGravity(true);
                armorStand.setMarker(true); // Makes it non-collidable
                armorStand.setSmall(false); // Keep normal size for better visibility
                armorStand.setNoBasePlate(true);
                armorStand.setShowArms(false);
                
                // Set a test item in the main hand
                ItemStack testItem = new ItemStack(net.minecraft.world.item.Items.DIAMOND);
                armorStand.setItemInHand(net.minecraft.world.InteractionHand.MAIN_HAND, testItem);
                
                // Add to world
                boolean added = level.addFreshEntity(armorStand);
                System.out.println("Armor stand added to world: " + added + ", Entity ID: " + armorStand.getId());
                
                newArmorStands.add(armorStand);
            }
        }
        
        // Store the armor stands for this session
        armorStands.put(sessionId, newArmorStands);
        System.out.println("Created " + newArmorStands.size() + " armor stands for session " + sessionId);
        return newArmorStands;
    }
    
    /**
     * Updates the items displayed by the armor stands
     * @param sessionId Session ID to update
     * @param items Array of 3 ItemStacks to display
     */
    public static void updateArmorStandItems(UUID sessionId, ItemStack[] items) {
        List<ArmorStand> stands = armorStands.get(sessionId);
        if (stands == null || items.length != 3) {
            return;
        }
        
        for (int i = 0; i < Math.min(stands.size(), 3); i++) {
            ArmorStand stand = stands.get(i);
            if (stand != null && stand.isAlive() && i < items.length) {
                stand.setItemInHand(net.minecraft.world.InteractionHand.MAIN_HAND, items[i].copy());
            }
        }
    }
    
    /**
     * Clears armor stands for a specific session
     * @param level The server level
     * @param sessionId Session ID to clear
     */
    public static void clearArmorStands(ServerLevel level, UUID sessionId) {
        List<ArmorStand> stands = armorStands.remove(sessionId);
        if (stands != null) {
            for (ArmorStand stand : stands) {
                if (stand != null && stand.isAlive()) {
                    stand.discard();
                }
            }
        }
    }
    
    /**
     * Clears all armor stands (useful for cleanup)
     * @param level The server level
     */
    public static void clearAllArmorStands(ServerLevel level) {
        for (UUID sessionId : new ArrayList<>(armorStands.keySet())) {
            clearArmorStands(level, sessionId);
        }
    }
    
    /**
     * Checks if armor stands are active for a session
     * @param sessionId Session ID to check
     * @return true if armor stands exist and are valid
     */
    public static boolean hasArmorStands(UUID sessionId) {
        List<ArmorStand> stands = armorStands.get(sessionId);
        if (stands == null || stands.isEmpty()) {
            return false;
        }
        
        // Check if any stands are still alive
        for (ArmorStand stand : stands) {
            if (stand != null && stand.isAlive()) {
                return true;
            }
        }
        
        // All stands are dead, remove from tracking
        armorStands.remove(sessionId);
        return false;
    }
    
    /**
     * Creates spinning effect for armor stands
     * @param sessionId Session ID
     * @param availableItems List of available items
     * @param animationTick Current animation tick
     */
    public static void updateSpinningArmorStands(UUID sessionId, List<ItemStack> availableItems, int animationTick) {
        if (availableItems.isEmpty()) {
            return;
        }
        
        ItemStack[] spinningItems = new ItemStack[3];
        for (int i = 0; i < 3; i++) {
            int itemIndex = (animationTick + i * 7) % availableItems.size();
            spinningItems[i] = availableItems.get(itemIndex).copy();
        }
        
        updateArmorStandItems(sessionId, spinningItems);
    }
}
