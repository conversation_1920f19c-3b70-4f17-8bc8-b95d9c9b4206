package com.example.casinomod.commands;

import com.mojang.brigadier.CommandDispatcher;
import com.mojang.brigadier.context.CommandContext;
import net.minecraft.commands.CommandSourceStack;
import net.minecraft.commands.Commands;
import net.minecraft.network.chat.Component;
import net.minecraft.server.level.ServerPlayer;
import net.minecraft.world.MenuProvider;
import net.minecraft.world.entity.player.Inventory;
import net.minecraft.world.entity.player.Player;
import net.minecraft.world.inventory.AbstractContainerMenu;
import net.minecraftforge.network.NetworkHooks;
import com.example.casinomod.menu.SlotMachineMenu;
import com.example.casinomod.server.SlotMachineManager;
import com.example.casinomod.server.SlotMachineItemManager;

public class SlotMachineCommand {
    
    public static void register(CommandDispatcher<CommandSourceStack> dispatcher) {
        dispatcher.register(Commands.literal("slotmachine")
                .executes(SlotMachineCommand::execute)
                .then(Commands.literal("clear")
                        .executes(SlotMachineCommand::clearFrames))
                .then(Commands.literal("items")
                        .then(Commands.literal("list")
                                .executes(SlotMachineCommand::listItems))
                        .then(Commands.literal("add")
                                .executes(SlotMachineCommand::addHeldItem))
                        .then(Commands.literal("remove")
                                .executes(SlotMachineCommand::removeHeldItem))
                        .then(Commands.literal("reset")
                                .executes(SlotMachineCommand::resetItems)))
                .then(Commands.literal("floating")
                        .executes(SlotMachineCommand::toggleFloating)));
    }
    
    private static int execute(CommandContext<CommandSourceStack> context) {
        if (context.getSource().getEntity() instanceof ServerPlayer player) {
            // Open the slot machine GUI
            NetworkHooks.openScreen(player, new MenuProvider() {
                @Override
                public Component getDisplayName() {
                    return Component.literal("Casino Slot Machine");
                }
                
                @Override
                public AbstractContainerMenu createMenu(int windowId, Inventory playerInventory, Player player) {
                    return new SlotMachineMenu(windowId, playerInventory);
                }
            });
            

            return 1;
        } else {
            context.getSource().sendFailure(Component.literal("This command can only be used by players!"));
            return 0;
        }
    }

    private static int clearFrames(CommandContext<CommandSourceStack> context) {
        if (context.getSource().getEntity() instanceof ServerPlayer player) {
            SlotMachineManager.clearPlayerFrames(player);
            return 1;
        } else {
            context.getSource().sendFailure(Component.literal("This command can only be used by players!"));
            return 0;
        }
    }

    private static int listItems(CommandContext<CommandSourceStack> context) {
        if (context.getSource().getEntity() instanceof ServerPlayer player) {
            SlotMachineItemManager.listItems(player);
            return 1;
        } else {
            context.getSource().sendFailure(Component.literal("This command can only be used by players!"));
            return 0;
        }
    }

    private static int addHeldItem(CommandContext<CommandSourceStack> context) {
        if (context.getSource().getEntity() instanceof ServerPlayer player) {
            SlotMachineItemManager.addHeldItem(player);
            return 1;
        } else {
            context.getSource().sendFailure(Component.literal("This command can only be used by players!"));
            return 0;
        }
    }

    private static int removeHeldItem(CommandContext<CommandSourceStack> context) {
        if (context.getSource().getEntity() instanceof ServerPlayer player) {
            SlotMachineItemManager.removeHeldItem(player);
            return 1;
        } else {
            context.getSource().sendFailure(Component.literal("This command can only be used by players!"));
            return 0;
        }
    }

    private static int resetItems(CommandContext<CommandSourceStack> context) {
        if (context.getSource().getEntity() instanceof ServerPlayer player) {
            SlotMachineItemManager.resetToConfig(player);
            return 1;
        } else {
            context.getSource().sendFailure(Component.literal("This command can only be used by players!"));
            return 0;
        }
    }

    private static int toggleFloating(CommandContext<CommandSourceStack> context) {
        if (context.getSource().getEntity() instanceof ServerPlayer player) {
            com.example.casinomod.Config.enableFloatingItems = !com.example.casinomod.Config.enableFloatingItems;
            String status = com.example.casinomod.Config.enableFloatingItems ? "enabled" : "disabled";
            player.sendSystemMessage(Component.literal("§6Floating items " + status + "! This affects new slot machine sessions."));
            return 1;
        } else {
            context.getSource().sendFailure(Component.literal("This command can only be used by players!"));
            return 0;
        }
    }

}
