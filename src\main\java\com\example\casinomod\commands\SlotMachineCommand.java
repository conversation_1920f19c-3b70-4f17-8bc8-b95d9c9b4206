package com.example.casinomod.commands;

import com.mojang.brigadier.CommandDispatcher;
import com.mojang.brigadier.context.CommandContext;
import net.minecraft.commands.CommandSourceStack;
import net.minecraft.commands.Commands;
import net.minecraft.network.chat.Component;
import net.minecraft.server.level.ServerPlayer;
import net.minecraft.world.MenuProvider;
import net.minecraft.world.entity.player.Inventory;
import net.minecraft.world.entity.player.Player;
import net.minecraft.world.inventory.AbstractContainerMenu;
import net.minecraftforge.network.NetworkHooks;
import com.example.casinomod.menu.SlotMachineMenu;

public class SlotMachineCommand {
    
    public static void register(CommandDispatcher<CommandSourceStack> dispatcher) {
        dispatcher.register(Commands.literal("slotmachine")
                .executes(SlotMachineCommand::execute));
    }
    
    private static int execute(CommandContext<CommandSourceStack> context) {
        if (context.getSource().getEntity() instanceof ServerPlayer player) {
            // Open the slot machine GUI
            NetworkHooks.openScreen(player, new MenuProvider() {
                @Override
                public Component getDisplayName() {
                    return Component.literal("Casino Slot Machine");
                }
                
                @Override
                public AbstractContainerMenu createMenu(int windowId, Inventory playerInventory, Player player) {
                    return new SlotMachineMenu(windowId, playerInventory);
                }
            });
            
            player.sendSystemMessage(Component.literal("Opening Casino Slot Machine!"));
            return 1;
        } else {
            context.getSource().sendFailure(Component.literal("This command can only be used by players!"));
            return 0;
        }
    }
}
