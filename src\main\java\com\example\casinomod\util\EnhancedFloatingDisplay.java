package com.example.casinomod.util;

import net.minecraft.server.level.ServerLevel;
import net.minecraft.world.entity.decoration.GlowItemFrame;
import net.minecraft.world.entity.item.ItemEntity;
import net.minecraft.world.item.ItemStack;
import net.minecraft.world.phys.Vec3;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.UUID;

public class EnhancedFloatingDisplay {
    
    // Track floating items for each slot machine session (each slot has multiple items for "larger" effect)
    private static final Map<UUID, List<List<ItemEntity>>> enhancedFloatingItems = new HashMap<>();
    
    /**
     * Creates enhanced floating item displays with multiple items per slot for larger appearance
     * @param level The server level
     * @param frames List of 3 Glow Item Frames
     * @param sessionId Unique session ID for tracking
     * @return List of lists of created floating item entities (3 slots, each with multiple items)
     */
    public static List<List<ItemEntity>> createEnhancedFloatingDisplay(ServerLevel level, List<GlowItemFrame> frames, UUID sessionId) {
        // Clear any existing floating items for this session
        clearEnhancedFloatingDisplay(level, sessionId);
        
        List<List<ItemEntity>> newEnhancedFloatingItems = new ArrayList<>();
        
        for (int i = 0; i < Math.min(frames.size(), 3); i++) {
            GlowItemFrame frame = frames.get(i);
            if (frame != null && frame.isAlive()) {
                List<ItemEntity> slotItems = new ArrayList<>();
                
                // Create multiple floating items in a pattern for "larger" appearance
                Vec3 framePos = frame.position();
                
                // Create a 2x2 pattern of items 2 blocks above the frame
                Vec3[] positions = {
                    framePos.add(-0.2, 2.0, -0.2), // Top-left
                    framePos.add(0.2, 2.0, -0.2),  // Top-right
                    framePos.add(-0.2, 2.0, 0.2),  // Bottom-left
                    framePos.add(0.2, 2.0, 0.2)    // Bottom-right
                };
                
                for (Vec3 pos : positions) {
                    // Start with a visible item (diamond) so it's immediately visible
                    ItemStack initialItem = new ItemStack(net.minecraft.world.item.Items.DIAMOND);
                    ItemEntity floatingItem = new ItemEntity(level, pos.x, pos.y, pos.z, initialItem);
                    
                    // Configure the floating item for visibility
                    floatingItem.setNoGravity(true);
                    floatingItem.setNeverPickUp();
                    floatingItem.setUnlimitedLifetime();
                    floatingItem.setDeltaMovement(Vec3.ZERO); // No movement
                    floatingItem.setGlowingTag(true); // Make it glow for visibility
                    
                    // Set a longer lifespan
                    floatingItem.lifespan = Integer.MAX_VALUE;
                    
                    // Add to world
                    level.addFreshEntity(floatingItem);
                    slotItems.add(floatingItem);
                }
                
                newEnhancedFloatingItems.add(slotItems);
            }
        }
        
        // Store the floating items for this session
        enhancedFloatingItems.put(sessionId, newEnhancedFloatingItems);
        return newEnhancedFloatingItems;
    }
    
    /**
     * Updates the items displayed by the enhanced floating entities
     * @param sessionId Session ID to update
     * @param items Array of 3 ItemStacks to display
     */
    public static void updateEnhancedFloatingDisplay(UUID sessionId, ItemStack[] items) {
        List<List<ItemEntity>> slotGroups = enhancedFloatingItems.get(sessionId);
        if (slotGroups == null || items.length != 3) {
            return;
        }
        
        for (int i = 0; i < Math.min(slotGroups.size(), 3); i++) {
            List<ItemEntity> slotItems = slotGroups.get(i);
            if (slotItems != null && i < items.length) {
                ItemStack itemToSet = items[i].isEmpty() ? new ItemStack(net.minecraft.world.item.Items.BARRIER) : items[i].copy();
                
                // Update all items in this slot group to show the same item
                for (ItemEntity entity : slotItems) {
                    if (entity != null && entity.isAlive()) {
                        entity.setItem(itemToSet);
                    }
                }
            }
        }
    }
    
    /**
     * Clears enhanced floating displays for a specific session
     * @param level The server level
     * @param sessionId Session ID to clear
     */
    public static void clearEnhancedFloatingDisplay(ServerLevel level, UUID sessionId) {
        List<List<ItemEntity>> slotGroups = enhancedFloatingItems.remove(sessionId);
        if (slotGroups != null) {
            for (List<ItemEntity> slotItems : slotGroups) {
                if (slotItems != null) {
                    for (ItemEntity entity : slotItems) {
                        if (entity != null && entity.isAlive()) {
                            entity.discard();
                        }
                    }
                }
            }
        }
    }
    
    /**
     * Clears all enhanced floating displays (useful for cleanup)
     * @param level The server level
     */
    public static void clearAllEnhancedFloatingDisplays(ServerLevel level) {
        for (UUID sessionId : new ArrayList<>(enhancedFloatingItems.keySet())) {
            clearEnhancedFloatingDisplay(level, sessionId);
        }
    }
    
    /**
     * Checks if enhanced floating displays are active for a session
     * @param sessionId Session ID to check
     * @return true if displays exist and are valid
     */
    public static boolean hasEnhancedFloatingDisplay(UUID sessionId) {
        List<List<ItemEntity>> slotGroups = enhancedFloatingItems.get(sessionId);
        if (slotGroups == null || slotGroups.isEmpty()) {
            return false;
        }
        
        // Check if any entities are still alive
        for (List<ItemEntity> slotItems : slotGroups) {
            if (slotItems != null) {
                for (ItemEntity entity : slotItems) {
                    if (entity != null && entity.isAlive()) {
                        return true;
                    }
                }
            }
        }
        
        // All entities are dead, remove from tracking
        enhancedFloatingItems.remove(sessionId);
        return false;
    }
    
    /**
     * Creates spinning effect for enhanced floating displays with rotation
     * @param sessionId Session ID
     * @param availableItems List of available items
     * @param animationTick Current animation tick
     */
    public static void updateSpinningEnhancedDisplay(UUID sessionId, List<ItemStack> availableItems, int animationTick) {
        if (availableItems.isEmpty()) {
            return;
        }
        
        ItemStack[] spinningItems = new ItemStack[3];
        for (int i = 0; i < 3; i++) {
            int itemIndex = (animationTick + i * 7) % availableItems.size();
            spinningItems[i] = availableItems.get(itemIndex).copy();
        }
        
        updateEnhancedFloatingDisplay(sessionId, spinningItems);
        
        // Add some movement animation during spinning for visual effect
        List<List<ItemEntity>> slotGroups = enhancedFloatingItems.get(sessionId);
        if (slotGroups != null) {
            for (List<ItemEntity> slotItems : slotGroups) {
                if (slotItems != null) {
                    for (int j = 0; j < slotItems.size(); j++) {
                        ItemEntity entity = slotItems.get(j);
                        if (entity != null && entity.isAlive()) {
                            // Create a subtle bobbing effect during spinning
                            double bobOffset = Math.sin((animationTick + j * 10) * 0.1) * 0.1;
                            Vec3 currentPos = entity.position();
                            entity.setPos(currentPos.x, currentPos.y + bobOffset * 0.1, currentPos.z);
                        }
                    }
                }
            }
        }
    }
}
