package com.example.casinomod.util;

import net.minecraft.core.BlockPos;
import net.minecraft.server.level.ServerLevel;
import net.minecraft.server.level.ServerPlayer;
import net.minecraft.world.entity.decoration.GlowItemFrame;
import net.minecraft.world.entity.decoration.ItemFrame;
import net.minecraft.world.item.ItemStack;
import net.minecraft.world.level.Level;
import net.minecraft.world.phys.AABB;
import com.example.casinomod.Config;

import java.util.ArrayList;
import java.util.List;

public class ItemFrameSlotDisplay {
    
    /**
     * Finds nearby Glow Item Frames that can be used for slot machine display
     * @param level The world level
     * @param playerPos The player's position
     * @param range The search range
     * @return List of exactly 3 Glow Item Frames, or empty list if not enough found
     */
    public static List<GlowItemFrame> findSlotItemFrames(Level level, BlockPos playerPos, int range) {
        if (!(level instanceof ServerLevel serverLevel)) {
            return new ArrayList<>();
        }
        
        // Create search area around player
        AABB searchArea = new AABB(
            playerPos.getX() - range, playerPos.getY() - range, playerPos.getZ() - range,
            playerPos.getX() + range, playerPos.getY() + range, playerPos.getZ() + range
        );
        
        // Find all Glow Item Frames in the area
        List<GlowItemFrame> allFrames = serverLevel.getEntitiesOfClass(
            GlowItemFrame.class, 
            searchArea,
            frame -> frame != null && frame.isAlive()
        );
        
        // Sort by distance to player for consistent ordering
        allFrames.sort((frame1, frame2) -> {
            double dist1 = frame1.blockPosition().distSqr(playerPos);
            double dist2 = frame2.blockPosition().distSqr(playerPos);
            return Double.compare(dist1, dist2);
        });
        
        // Return exactly 3 frames if available
        if (allFrames.size() >= 3) {
            return allFrames.subList(0, 3);
        }
        
        return new ArrayList<>(); // Not enough frames found
    }
    
    /**
     * Updates the items displayed in the slot item frames
     * @param frames List of 3 Glow Item Frames
     * @param items Array of 3 ItemStacks to display
     */
    public static void updateSlotFrames(List<GlowItemFrame> frames, ItemStack[] items) {
        if (frames.size() != 3 || items.length != 3) {
            return;
        }
        
        for (int i = 0; i < 3; i++) {
            GlowItemFrame frame = frames.get(i);
            ItemStack item = items[i];
            
            if (frame != null && frame.isAlive()) {
                // Set the item in the frame
                frame.setItem(item.copy(), false);
            }
        }
    }
    
    /**
     * Clears all items from the slot frames
     * @param frames List of Glow Item Frames to clear
     */
    public static void clearSlotFrames(List<GlowItemFrame> frames) {
        for (GlowItemFrame frame : frames) {
            if (frame != null && frame.isAlive()) {
                frame.setItem(ItemStack.EMPTY, false);
            }
        }
    }
    
    /**
     * Checks if item frame display is enabled and if enough frames are available
     * @param player The player using the slot machine
     * @return List of 3 Glow Item Frames if available and enabled, empty list otherwise
     */
    public static List<GlowItemFrame> getAvailableSlotFrames(ServerPlayer player) {
        // Check if item frame display is enabled in config
        if (!Config.enableItemFrameDisplay) {
            return new ArrayList<>();
        }
        
        // Find nearby frames
        List<GlowItemFrame> frames = findSlotItemFrames(
            player.level(), 
            player.blockPosition(), 
            Config.itemFrameSearchRange
        );
        
        return frames;
    }
    
    /**
     * Validates that all frames in the list are still valid and alive
     * @param frames List of frames to validate
     * @return true if all frames are valid, false otherwise
     */
    public static boolean validateFrames(List<GlowItemFrame> frames) {
        if (frames.size() != 3) {
            return false;
        }
        
        for (GlowItemFrame frame : frames) {
            if (frame == null || !frame.isAlive()) {
                return false;
            }
        }
        
        return true;
    }
    
    /**
     * Creates a spinning effect by rapidly changing items in frames
     * @param frames List of 3 Glow Item Frames
     * @param availableItems List of items that can appear
     * @param animationTick Current animation tick for variation
     */
    public static void updateSpinningFrames(List<GlowItemFrame> frames, List<ItemStack> availableItems, int animationTick) {
        if (!validateFrames(frames) || availableItems.isEmpty()) {
            return;
        }
        
        ItemStack[] spinningItems = new ItemStack[3];
        
        for (int i = 0; i < 3; i++) {
            // Create spinning effect by rapidly changing items
            int itemIndex = (animationTick + i * 7) % availableItems.size();
            spinningItems[i] = availableItems.get(itemIndex).copy();
        }
        
        updateSlotFrames(frames, spinningItems);
    }
}
