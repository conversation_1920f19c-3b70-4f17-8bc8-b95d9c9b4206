package com.example.casinomod.server;

import net.minecraft.server.level.ServerLevel;
import net.minecraft.server.level.ServerPlayer;
import net.minecraft.world.item.Item;
import net.minecraft.world.item.Items;

import java.util.HashMap;
import java.util.Map;
import java.util.UUID;

public class DeathMinigameEffects {
    
    // Track players with pending death effects
    private static final Map<UUID, PendingDeathEffect> pendingEffects = new HashMap<>();
    
    public static class PendingDeathEffect {
        public final DeathType type;
        public final long triggerTime;
        public final ServerPlayer player;
        
        public PendingDeathEffect(DeathType type, long triggerTime, ServerPlayer player) {
            this.type = type;
            this.triggerTime = triggerTime;
            this.player = player;
        }
    }
    
    public enum DeathType {
        ENDER_PEARL_FALL,
        TNT_EXPLOSION,
        GOLDEN_APPLE_SAFE
    }
    
    /**
     * Triggers a death effect based on the slot machine result
     * @param player The player who used the slot machine
     * @param resultItem The item that appeared on all 3 slots
     */
    public static void triggerDeathEffect(ServerPlayer player, Item resultItem) {
        DeathType deathType;
        long delay = 0; // Immediate by default
        
        if (resultItem == Items.ENDER_PEARL) {
            deathType = DeathType.ENDER_PEARL_FALL;
            executeEnderPearlDeath(player);
        } else if (resultItem == Items.TNT) {
            deathType = DeathType.TNT_EXPLOSION;
            delay = 1000; // 1 second delay
            pendingEffects.put(player.getUUID(), new PendingDeathEffect(deathType, System.currentTimeMillis() + delay, player));
        } else if (resultItem == Items.GOLDEN_APPLE) {
            deathType = DeathType.GOLDEN_APPLE_SAFE;
            executeGoldenAppleSafe(player);
        } else {
            // Shouldn't happen with fixed items, but just in case
            return;
        }
    }
    
    /**
     * Ender Pearl Death: Launch player high into the air and make them fall fast
     */
    private static void executeEnderPearlDeath(ServerPlayer player) {
        // Launch player high into the air with much stronger force
        player.setDeltaMovement(0, 4.5, 0); // Very strong upward velocity, reset horizontal movement
        player.hurtMarked = true; // Force velocity update

        // Schedule the fast fall after 3 seconds (when they're at peak height)
        pendingEffects.put(player.getUUID(), new PendingDeathEffect(DeathType.ENDER_PEARL_FALL, System.currentTimeMillis() + 3000, player));
    }
    
    /**
     * TNT Death: Create explosion effect that damages player but doesn't break blocks
     */
    private static void executeTntDeath(ServerPlayer player) {
        if (player.level() instanceof ServerLevel serverLevel) {
            // Create explosion at player's location that damages entities but not blocks
            serverLevel.explode(
                null,                    // No source entity
                player.getX(),           // X position
                player.getY(),           // Y position
                player.getZ(),           // Z position
                4.0f,                    // Explosion power (strong enough to kill)
                false,                   // Don't cause fire
                net.minecraft.world.level.Level.ExplosionInteraction.NONE  // Don't break blocks
            );

            // Explosion happens silently
        }
    }
    
    /**
     * Golden Apple Safe: Give player beneficial effects (they survived!)
     */
    private static void executeGoldenAppleSafe(ServerPlayer player) {
        // Give beneficial effects as a reward for getting the safe option
        player.addEffect(new net.minecraft.world.effect.MobEffectInstance(
            net.minecraft.world.effect.MobEffects.REGENERATION,
            200, // 10 seconds
            2    // Level 3 regeneration
        ));

        // Add absorption hearts
        player.addEffect(new net.minecraft.world.effect.MobEffectInstance(
            net.minecraft.world.effect.MobEffects.ABSORPTION,
            2400, // 2 minutes
            1     // Level 2 (4 extra hearts)
        ));

        // Add resistance for protection
        player.addEffect(new net.minecraft.world.effect.MobEffectInstance(
            net.minecraft.world.effect.MobEffects.DAMAGE_RESISTANCE,
            600, // 30 seconds
            1    // Level 2
        ));

        // Heal the player to full health
        player.setHealth(player.getMaxHealth());
    }
    
    /**
     * Process pending death effects (called from server tick)
     */
    public static void processPendingEffects() {
        long currentTime = System.currentTimeMillis();
        
        pendingEffects.entrySet().removeIf(entry -> {
            PendingDeathEffect effect = entry.getValue();
            
            if (currentTime >= effect.triggerTime) {
                // Execute the delayed effect
                switch (effect.type) {
                    case TNT_EXPLOSION:
                        executeTntDeath(effect.player);
                        break;
                    case ENDER_PEARL_FALL:
                        // Make player fall really fast with extreme downward velocity
                        effect.player.setDeltaMovement(0, -8.0, 0); // Very fast downward velocity
                        effect.player.hurtMarked = true; // Force velocity update
                        break;
                    case GOLDEN_APPLE_SAFE:
                        // Golden apple effects are applied immediately, no delayed effect needed
                        break;
                }
                return true; // Remove from pending effects
            }
            
            return false; // Keep in pending effects
        });
    }
    
    /**
     * Clear pending effects for a player (e.g., when they disconnect)
     */
    public static void clearPlayerEffects(UUID playerId) {
        pendingEffects.remove(playerId);
    }
    
    /**
     * Check if all three items are the same (for triggering death effects)
     */
    public static boolean areAllItemsSame(net.minecraft.world.item.ItemStack[] items) {
        if (items.length != 3) return false;
        
        Item firstItem = items[0].getItem();
        return items[1].getItem() == firstItem && items[2].getItem() == firstItem;
    }
}
