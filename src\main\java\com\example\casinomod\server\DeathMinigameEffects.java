package com.example.casinomod.server;

import net.minecraft.core.BlockPos;
import net.minecraft.server.level.ServerLevel;
import net.minecraft.server.level.ServerPlayer;
import net.minecraft.world.entity.item.PrimedTnt;
import net.minecraft.world.item.Item;
import net.minecraft.world.item.Items;
import net.minecraft.world.phys.Vec3;

import java.util.HashMap;
import java.util.Map;
import java.util.UUID;

public class DeathMinigameEffects {
    
    // Track players with pending death effects
    private static final Map<UUID, PendingDeathEffect> pendingEffects = new HashMap<>();
    
    public static class PendingDeathEffect {
        public final DeathType type;
        public final long triggerTime;
        public final ServerPlayer player;
        
        public PendingDeathEffect(DeathType type, long triggerTime, ServerPlayer player) {
            this.type = type;
            this.triggerTime = triggerTime;
            this.player = player;
        }
    }
    
    public enum DeathType {
        ENDER_PEARL_FALL,
        TNT_EXPLOSION,
        GOLDEN_APPLE_POISON
    }
    
    /**
     * Triggers a death effect based on the slot machine result
     * @param player The player who used the slot machine
     * @param resultItem The item that appeared on all 3 slots
     */
    public static void triggerDeathEffect(ServerPlayer player, Item resultItem) {
        DeathType deathType;
        long delay = 0; // Immediate by default
        
        if (resultItem == Items.ENDER_PEARL) {
            deathType = DeathType.ENDER_PEARL_FALL;
            executeEnderPearlDeath(player);
            player.sendSystemMessage(net.minecraft.network.chat.Component.literal("§5💀 ENDER PEARL DEATH! You're going for a ride! 💀"));
        } else if (resultItem == Items.TNT) {
            deathType = DeathType.TNT_EXPLOSION;
            delay = 1000; // 1 second delay
            pendingEffects.put(player.getUUID(), new PendingDeathEffect(deathType, System.currentTimeMillis() + delay, player));
            player.sendSystemMessage(net.minecraft.network.chat.Component.literal("§c💀 TNT DEATH! You have 1 second to live! 💀"));
        } else if (resultItem == Items.GOLDEN_APPLE) {
            deathType = DeathType.GOLDEN_APPLE_POISON;
            executeGoldenAppleDeath(player);
            player.sendSystemMessage(net.minecraft.network.chat.Component.literal("§6💀 GOLDEN APPLE DEATH! Enjoy your last meal! 💀"));
        } else {
            // Shouldn't happen with fixed items, but just in case
            player.sendSystemMessage(net.minecraft.network.chat.Component.literal("§7You got lucky this time..."));
            return;
        }
    }
    
    /**
     * Ender Pearl Death: Launch player high into the air and make them fall fast
     */
    private static void executeEnderPearlDeath(ServerPlayer player) {
        // Launch player high into the air
        Vec3 currentVelocity = player.getDeltaMovement();
        player.setDeltaMovement(currentVelocity.x, 3.0, currentVelocity.z); // Strong upward velocity
        
        // Schedule the fast fall after a short delay
        pendingEffects.put(player.getUUID(), new PendingDeathEffect(DeathType.ENDER_PEARL_FALL, System.currentTimeMillis() + 2000, player));
    }
    
    /**
     * TNT Death: Spawn TNT at player's location
     */
    private static void executeTntDeath(ServerPlayer player) {
        if (player.level() instanceof ServerLevel serverLevel) {
            BlockPos playerPos = player.blockPosition();
            
            // Spawn multiple TNT entities around the player
            for (int i = 0; i < 3; i++) {
                PrimedTnt tnt = new PrimedTnt(serverLevel, 
                    playerPos.getX() + (Math.random() - 0.5) * 2, 
                    playerPos.getY() + 1, 
                    playerPos.getZ() + (Math.random() - 0.5) * 2, 
                    null);
                tnt.setFuse(10); // Very short fuse (0.5 seconds)
                serverLevel.addFreshEntity(tnt);
            }
        }
    }
    
    /**
     * Golden Apple Death: Apply deadly poison effect
     */
    private static void executeGoldenAppleDeath(ServerPlayer player) {
        // Apply deadly poison effect
        player.addEffect(new net.minecraft.world.effect.MobEffectInstance(
            net.minecraft.world.effect.MobEffects.POISON, 
            200, // 10 seconds
            4    // Level 5 poison (very deadly)
        ));
        
        // Also add wither effect for extra deadliness
        player.addEffect(new net.minecraft.world.effect.MobEffectInstance(
            net.minecraft.world.effect.MobEffects.WITHER, 
            100, // 5 seconds
            2    // Level 3 wither
        ));
        
        // Add nausea for disorientation
        player.addEffect(new net.minecraft.world.effect.MobEffectInstance(
            net.minecraft.world.effect.MobEffects.CONFUSION, 
            300, // 15 seconds
            1    // Level 2
        ));
    }
    
    /**
     * Process pending death effects (called from server tick)
     */
    public static void processPendingEffects() {
        long currentTime = System.currentTimeMillis();
        
        pendingEffects.entrySet().removeIf(entry -> {
            PendingDeathEffect effect = entry.getValue();
            
            if (currentTime >= effect.triggerTime) {
                // Execute the delayed effect
                switch (effect.type) {
                    case TNT_EXPLOSION:
                        executeTntDeath(effect.player);
                        break;
                    case ENDER_PEARL_FALL:
                        // Make player fall really fast
                        Vec3 currentVelocity = effect.player.getDeltaMovement();
                        effect.player.setDeltaMovement(currentVelocity.x, -5.0, currentVelocity.z); // Fast downward velocity
                        break;
                }
                return true; // Remove from pending effects
            }
            
            return false; // Keep in pending effects
        });
    }
    
    /**
     * Clear pending effects for a player (e.g., when they disconnect)
     */
    public static void clearPlayerEffects(UUID playerId) {
        pendingEffects.remove(playerId);
    }
    
    /**
     * Check if all three items are the same (for triggering death effects)
     */
    public static boolean areAllItemsSame(net.minecraft.world.item.ItemStack[] items) {
        if (items.length != 3) return false;
        
        Item firstItem = items[0].getItem();
        return items[1].getItem() == firstItem && items[2].getItem() == firstItem;
    }
}
