package com.example.casinomod.client.screen;

import com.example.casinomod.Config;
import com.example.casinomod.menu.SlotMachineMenu;
import net.minecraft.client.gui.GuiGraphics;
import net.minecraft.client.gui.screens.inventory.AbstractContainerScreen;
import net.minecraft.network.chat.Component;
import net.minecraft.resources.ResourceLocation;
import net.minecraft.world.entity.player.Inventory;
import net.minecraft.world.item.ItemStack;
import net.minecraft.world.item.Items;
import net.minecraftforge.api.distmarker.Dist;
import net.minecraftforge.api.distmarker.OnlyIn;

import java.util.ArrayList;
import java.util.List;
import java.util.Random;

@OnlyIn(Dist.CLIENT)
public class SlotMachineScreen extends AbstractContainerScreen<SlotMachineMenu> {
    
    private static final ResourceLocation TEXTURE = ResourceLocation.fromNamespaceAndPath("casinomod", "textures/gui/slot_machine.png");
    
    // Slot machine state
    private boolean isSpinning = false;
    private int spinTimer = 0;
    private int spinDuration = 100; // ticks - will be set from config
    
    // Slot items
    private final List<ItemStack> slotItems = new ArrayList<>();
    private final ItemStack[] currentSlots = new ItemStack[3];
    private final ItemStack[] finalSlots = new ItemStack[3];
    private final Random random = new Random();
    
    // Animation
    private int animationTick = 0;
    
    public SlotMachineScreen(SlotMachineMenu menu, Inventory playerInventory, Component title) {
        super(menu, playerInventory, title);
        this.imageWidth = 176;
        this.imageHeight = 166;

        // Set spin duration from config
        this.spinDuration = Config.spinDuration > 0 ? Config.spinDuration : 100;

        initializeSlotItems();
        resetSlots();
    }
    
    private void initializeSlotItems() {
        // Add configurable items to the slot machine from config
        if (Config.slotMachineItems != null && !Config.slotMachineItems.isEmpty()) {
            Config.slotMachineItems.forEach(item -> slotItems.add(new ItemStack(item)));
        } else {
            // Fallback items if config is empty
            slotItems.add(new ItemStack(Items.DIAMOND));
            slotItems.add(new ItemStack(Items.EMERALD));
            slotItems.add(new ItemStack(Items.GOLD_INGOT));
            slotItems.add(new ItemStack(Items.IRON_INGOT));
            slotItems.add(new ItemStack(Items.REDSTONE));
            slotItems.add(new ItemStack(Items.LAPIS_LAZULI));
            slotItems.add(new ItemStack(Items.COAL));
            slotItems.add(new ItemStack(Items.APPLE));
            slotItems.add(new ItemStack(Items.BREAD));
            slotItems.add(new ItemStack(Items.COOKED_BEEF));
        }
    }
    
    private void resetSlots() {
        for (int i = 0; i < 3; i++) {
            currentSlots[i] = getRandomItem();
            finalSlots[i] = getRandomItem();
        }
    }
    
    private ItemStack getRandomItem() {
        return slotItems.get(random.nextInt(slotItems.size())).copy();
    }
    
    @Override
    protected void renderBg(GuiGraphics guiGraphics, float partialTick, int mouseX, int mouseY) {
        int x = (this.width - this.imageWidth) / 2;
        int y = (this.height - this.imageHeight) / 2;
        
        // Draw background (we'll create a simple colored background for now)
        guiGraphics.fill(x, y, x + this.imageWidth, y + this.imageHeight, 0xFF8B4513); // Brown background
        
        // Draw slot machine frame
        guiGraphics.fill(x + 20, y + 20, x + 156, y + 80, 0xFF2F2F2F); // Dark gray frame
        
        // Draw individual slot backgrounds
        for (int i = 0; i < 3; i++) {
            int slotX = x + 30 + (i * 40);
            int slotY = y + 30;
            guiGraphics.fill(slotX, slotY, slotX + 32, slotY + 32, 0xFF000000); // Black slot background
        }
        
        // Draw spin button
        int buttonX = x + 70;
        int buttonY = y + 90;
        int buttonColor = isSpinning ? 0xFF666666 : 0xFF00AA00; // Gray when spinning, green when ready
        guiGraphics.fill(buttonX, buttonY, buttonX + 36, buttonY + 20, buttonColor);
        
        // Draw button text
        Component buttonText = isSpinning ? Component.literal("SPINNING...") : Component.literal("SPIN!");
        guiGraphics.drawCenteredString(this.font, buttonText, buttonX + 18, buttonY + 6, 0xFFFFFF);
    }
    
    @Override
    public void render(GuiGraphics guiGraphics, int mouseX, int mouseY, float partialTick) {
        this.renderBackground(guiGraphics);
        super.render(guiGraphics, mouseX, mouseY, partialTick);
        this.renderTooltip(guiGraphics, mouseX, mouseY);
        
        // Render slot items
        renderSlotItems(guiGraphics);
    }
    
    private void renderSlotItems(GuiGraphics guiGraphics) {
        int x = (this.width - this.imageWidth) / 2;
        int y = (this.height - this.imageHeight) / 2;
        
        for (int i = 0; i < 3; i++) {
            int slotX = x + 30 + (i * 40) + 8;
            int slotY = y + 30 + 8;
            
            ItemStack itemToRender = isSpinning ? getSpinningItem(i) : currentSlots[i];
            guiGraphics.renderItem(itemToRender, slotX, slotY);
        }
    }
    
    private ItemStack getSpinningItem(int slotIndex) {
        // Create spinning effect by rapidly changing items
        int itemIndex = (animationTick + slotIndex * 7) % slotItems.size();
        return slotItems.get(itemIndex);
    }
    
    @Override
    public boolean mouseClicked(double mouseX, double mouseY, int button) {
        if (button == 0) { // Left click
            int x = (this.width - this.imageWidth) / 2;
            int y = (this.height - this.imageHeight) / 2;
            
            // Check if spin button was clicked
            int buttonX = x + 70;
            int buttonY = y + 90;
            
            if (mouseX >= buttonX && mouseX <= buttonX + 36 && 
                mouseY >= buttonY && mouseY <= buttonY + 20 && !isSpinning) {
                startSpin();
                return true;
            }
        }
        
        return super.mouseClicked(mouseX, mouseY, button);
    }
    
    private void startSpin() {
        if (!isSpinning) {
            isSpinning = true;
            spinTimer = 0;
            
            // Set final results
            for (int i = 0; i < 3; i++) {
                finalSlots[i] = getRandomItem();
            }
        }
    }
    
    @Override
    protected void containerTick() {
        super.containerTick();
        animationTick++;
        
        if (isSpinning) {
            spinTimer++;
            
            if (spinTimer >= spinDuration) {
                // Stop spinning
                isSpinning = false;
                spinTimer = 0;
                
                // Set final results
                System.arraycopy(finalSlots, 0, currentSlots, 0, 3);
                
                // Check for wins
                checkForWin();
            }
        }
    }
    
    private void checkForWin() {
        // Check if all three slots match
        if (currentSlots[0].getItem() == currentSlots[1].getItem() && 
            currentSlots[1].getItem() == currentSlots[2].getItem()) {
            
            // Player wins!
            if (minecraft != null && minecraft.player != null) {
                minecraft.player.sendSystemMessage(Component.literal("§6JACKPOT! You won with " + 
                    currentSlots[0].getHoverName().getString() + "!"));
            }
        } else if (currentSlots[0].getItem() == currentSlots[1].getItem() || 
                   currentSlots[1].getItem() == currentSlots[2].getItem() || 
                   currentSlots[0].getItem() == currentSlots[2].getItem()) {
            
            // Partial win
            if (minecraft != null && minecraft.player != null) {
                minecraft.player.sendSystemMessage(Component.literal("§eNice! Two matching items!"));
            }
        } else {
            // No win
            if (minecraft != null && minecraft.player != null) {
                minecraft.player.sendSystemMessage(Component.literal("§7Better luck next time!"));
            }
        }
    }
}
