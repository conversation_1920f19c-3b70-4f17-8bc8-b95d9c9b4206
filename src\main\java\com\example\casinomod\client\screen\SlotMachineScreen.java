package com.example.casinomod.client.screen;

import com.example.casinomod.Config;
import com.example.casinomod.menu.SlotMachineMenu;
import com.example.casinomod.network.SlotMachinePackets;
import net.minecraft.client.gui.GuiGraphics;
import net.minecraft.client.gui.screens.inventory.AbstractContainerScreen;
import net.minecraft.network.chat.Component;
import net.minecraft.resources.ResourceLocation;
import net.minecraft.world.entity.player.Inventory;
import net.minecraft.world.item.ItemStack;
import net.minecraft.world.item.Items;
import net.minecraftforge.api.distmarker.Dist;
import net.minecraftforge.api.distmarker.OnlyIn;


import java.util.ArrayList;
import java.util.List;
import java.util.Random;

@OnlyIn(Dist.CLIENT)
public class SlotMachineScreen extends AbstractContainerScreen<SlotMachineMenu> {
    
    private static final ResourceLocation TEXTURE = ResourceLocation.fromNamespaceAndPath("casinomod", "textures/gui/slot_machine.png");
    
    // Slot machine state
    private boolean isSpinning = false;
    private int spinTimer = 0;
    private int spinDuration = 100; // ticks - will be set from config
    
    // Slot items
    private final List<ItemStack> slotItems = new ArrayList<>();
    private final ItemStack[] currentSlots = new ItemStack[3];
    private final ItemStack[] finalSlots = new ItemStack[3];
    private final ItemStack[] syncedItems = new ItemStack[3]; // Items synced from server
    private final Random random = new Random();
    private boolean useSyncedItems = false;
    
    // Animation
    private int animationTick = 0;
    
    public SlotMachineScreen(SlotMachineMenu menu, Inventory playerInventory, Component title) {
        super(menu, playerInventory, title);
        this.imageWidth = 200;
        this.imageHeight = 140;

        // Set spin duration from config
        this.spinDuration = Config.spinDuration > 0 ? Config.spinDuration : 100;

        initializeSlotItems();
        resetSlots();
    }
    
    private void initializeSlotItems() {
        // Add configurable items to the slot machine from config
        if (Config.slotMachineItems != null && !Config.slotMachineItems.isEmpty()) {
            Config.slotMachineItems.forEach(item -> slotItems.add(new ItemStack(item)));
        } else {
            // Fallback items if config is empty
            slotItems.add(new ItemStack(Items.DIAMOND));
            slotItems.add(new ItemStack(Items.EMERALD));
            slotItems.add(new ItemStack(Items.GOLD_INGOT));
            slotItems.add(new ItemStack(Items.IRON_INGOT));
            slotItems.add(new ItemStack(Items.REDSTONE));
            slotItems.add(new ItemStack(Items.LAPIS_LAZULI));
            slotItems.add(new ItemStack(Items.COAL));
            slotItems.add(new ItemStack(Items.APPLE));
            slotItems.add(new ItemStack(Items.BREAD));
            slotItems.add(new ItemStack(Items.COOKED_BEEF));
        }
    }
    
    private void resetSlots() {
        for (int i = 0; i < 3; i++) {
            currentSlots[i] = getRandomItem();
            finalSlots[i] = getRandomItem();
        }
    }
    
    private ItemStack getRandomItem() {
        return slotItems.get(random.nextInt(slotItems.size())).copy();
    }
    
    @Override
    protected void renderBg(GuiGraphics guiGraphics, float partialTick, int mouseX, int mouseY) {
        int x = (this.width - this.imageWidth) / 2;
        int y = (this.height - this.imageHeight) / 2;

        // Draw main background with gradient effect
        guiGraphics.fill(x, y, x + this.imageWidth, y + this.imageHeight, 0xFF8B4513); // Brown background
        guiGraphics.fill(x + 2, y + 2, x + this.imageWidth - 2, y + this.imageHeight - 2, 0xFFA0522D); // Lighter brown border

        // Draw slot machine frame with better styling
        guiGraphics.fill(x + 15, y + 15, x + this.imageWidth - 15, y + 75, 0xFF2F2F2F); // Dark gray frame
        guiGraphics.fill(x + 17, y + 17, x + this.imageWidth - 17, y + 73, 0xFF1A1A1A); // Inner shadow

        // Draw individual slot backgrounds with borders
        for (int i = 0; i < 3; i++) {
            int slotX = x + 25 + (i * 50);
            int slotY = y + 25;

            // Slot border
            guiGraphics.fill(slotX - 2, slotY - 2, slotX + 36, slotY + 36, 0xFFFFFFFF); // White border
            guiGraphics.fill(slotX, slotY, slotX + 32, slotY + 32, 0xFF000000); // Black slot background
        }

        // Draw spin button with hover effect
        int buttonX = x + 75;
        int buttonY = y + 90;
        int buttonWidth = 50;
        int buttonHeight = 25;

        boolean isHovering = mouseX >= buttonX && mouseX <= buttonX + buttonWidth &&
                           mouseY >= buttonY && mouseY <= buttonY + buttonHeight;

        int buttonColor;
        if (isSpinning) {
            buttonColor = 0xFF666666; // Gray when spinning
        } else if (isHovering) {
            buttonColor = 0xFF00DD00; // Bright green when hovering
        } else {
            buttonColor = 0xFF00AA00; // Normal green
        }

        // Button with border
        guiGraphics.fill(buttonX - 1, buttonY - 1, buttonX + buttonWidth + 1, buttonY + buttonHeight + 1, 0xFF000000); // Black border
        guiGraphics.fill(buttonX, buttonY, buttonX + buttonWidth, buttonY + buttonHeight, buttonColor);

        // Button text
        Component buttonText = isSpinning ? Component.literal("SPINNING...") : Component.literal("SPIN!");
        int textColor = isSpinning ? 0xFFAAAAAA : 0xFFFFFFFF;
        guiGraphics.drawCenteredString(this.font, buttonText, buttonX + buttonWidth / 2, buttonY + 8, textColor);

        // Draw title
        Component title = Component.literal("§6§lCASINO SLOT MACHINE");
        guiGraphics.drawCenteredString(this.font, title, x + this.imageWidth / 2, y + 5, 0xFFFFD700);
    }
    
    @Override
    public void render(GuiGraphics guiGraphics, int mouseX, int mouseY, float partialTick) {
        this.renderBackground(guiGraphics);
        super.render(guiGraphics, mouseX, mouseY, partialTick);

        // Render slot items
        renderSlotItems(guiGraphics);

        // Don't render tooltips for this interface - we want it clean
    }

    @Override
    protected void renderLabels(GuiGraphics guiGraphics, int mouseX, int mouseY) {
        // Override to prevent inventory labels from showing
        // We don't want any text labels in our casino interface
    }
    
    private void renderSlotItems(GuiGraphics guiGraphics) {
        int x = (this.width - this.imageWidth) / 2;
        int y = (this.height - this.imageHeight) / 2;

        for (int i = 0; i < 3; i++) {
            int slotX = x + 25 + (i * 50) + 8;
            int slotY = y + 25 + 8;

            ItemStack itemToRender;
            if (useSyncedItems && syncedItems[i] != null) {
                // Use items synced from server for perfect synchronization
                itemToRender = syncedItems[i];
            } else {
                // Fallback to local items
                itemToRender = isSpinning ? getSpinningItem(i) : currentSlots[i];
            }

            guiGraphics.renderItem(itemToRender, slotX, slotY);
        }
    }
    
    private ItemStack getSpinningItem(int slotIndex) {
        // Create spinning effect by rapidly changing items
        int itemIndex = (animationTick + slotIndex * 7) % slotItems.size();
        return slotItems.get(itemIndex);
    }
    
    @Override
    public boolean mouseClicked(double mouseX, double mouseY, int button) {
        if (button == 0) { // Left click
            int x = (this.width - this.imageWidth) / 2;
            int y = (this.height - this.imageHeight) / 2;

            // Check if spin button was clicked
            int buttonX = x + 75;
            int buttonY = y + 90;
            int buttonWidth = 50;
            int buttonHeight = 25;

            if (mouseX >= buttonX && mouseX <= buttonX + buttonWidth &&
                mouseY >= buttonY && mouseY <= buttonY + buttonHeight && !isSpinning) {
                startSpin();
                return true;
            }
        }

        return super.mouseClicked(mouseX, mouseY, button);
    }

    @Override
    public boolean keyPressed(int keyCode, int scanCode, int modifiers) {
        // Allow ESC to close the interface
        if (keyCode == 256) { // ESC key
            this.onClose();
            return true;
        }
        return super.keyPressed(keyCode, scanCode, modifiers);
    }

    @Override
    public boolean isPauseScreen() {
        return false; // Don't pause the game when this screen is open
    }

    @Override
    public void onClose() {
        // Send stop packet to server when closing
        SlotMachinePackets.INSTANCE.sendToServer(new SlotMachinePackets.SpinStopPacket());
        super.onClose();
    }

    /**
     * Updates the slot items with data synced from the server
     * This ensures perfect synchronization between GUI and Glow Item Frames
     */
    public void updateSyncedItems(ItemStack[] items) {
        if (items.length >= 3) {
            System.arraycopy(items, 0, syncedItems, 0, 3);
            useSyncedItems = true;
        }
    }
    
    private void startSpin() {
        if (!isSpinning) {
            isSpinning = true;
            spinTimer = 0;
            useSyncedItems = true; // Use server-synced items for perfect synchronization

            // Set final results (fallback only)
            for (int i = 0; i < 3; i++) {
                finalSlots[i] = getRandomItem();
            }

            // Send packet to server to start item frame display
            SlotMachinePackets.INSTANCE.sendToServer(new SlotMachinePackets.SpinStartPacket());
        }
    }
    
    @Override
    protected void containerTick() {
        super.containerTick();
        animationTick++;

        if (isSpinning) {
            spinTimer++;

            if (spinTimer >= spinDuration) {
                // Stop spinning
                isSpinning = false;
                spinTimer = 0;

                // If using synced items, the final results will come from server
                if (!useSyncedItems) {
                    // Set final results (fallback only)
                    System.arraycopy(finalSlots, 0, currentSlots, 0, 3);
                    checkForWin();
                }
            }
        }
    }
    
    private void checkForWin() {
        // Check if all three slots match
        if (currentSlots[0].getItem() == currentSlots[1].getItem() && 
            currentSlots[1].getItem() == currentSlots[2].getItem()) {
            
            // Player wins!
            if (minecraft != null && minecraft.player != null) {
                minecraft.player.sendSystemMessage(Component.literal("§6JACKPOT! You won with " + 
                    currentSlots[0].getHoverName().getString() + "!"));
            }
        } else if (currentSlots[0].getItem() == currentSlots[1].getItem() || 
                   currentSlots[1].getItem() == currentSlots[2].getItem() || 
                   currentSlots[0].getItem() == currentSlots[2].getItem()) {
            
            // Partial win
            if (minecraft != null && minecraft.player != null) {
                minecraft.player.sendSystemMessage(Component.literal("§eNice! Two matching items!"));
            }
        } else {
            // No win
            if (minecraft != null && minecraft.player != null) {
                minecraft.player.sendSystemMessage(Component.literal("§7Better luck next time!"));
            }
        }
    }
}
