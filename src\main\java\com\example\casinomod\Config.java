package com.example.casinomod;

import net.minecraft.resources.ResourceLocation;
import net.minecraft.world.item.Item;
import net.minecraftforge.common.ForgeConfigSpec;
import net.minecraftforge.eventbus.api.SubscribeEvent;
import net.minecraftforge.fml.common.Mod;
import net.minecraftforge.fml.event.config.ModConfigEvent;
import net.minecraftforge.registries.ForgeRegistries;

import java.util.Arrays;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

// An example config class. This is not required, but it's a good idea to have one to keep your config organized.
// Demonstrates how to use Forge's config APIs
@Mod.EventBusSubscriber(modid = CasinoMod.MODID, bus = Mod.EventBusSubscriber.Bus.MOD)
public class Config
{
    private static final ForgeConfigSpec.Builder BUILDER = new ForgeConfigSpec.Builder();

    private static final ForgeConfigSpec.BooleanValue LOG_DIRT_BLOCK = BUILDER
            .comment("Whether to log the dirt block on common setup")
            .define("logDirtBlock", true);

    private static final ForgeConfigSpec.IntValue MAGIC_NUMBER = BUILDER
            .comment("A magic number")
            .defineInRange("magicNumber", 42, 0, Integer.MAX_VALUE);

    public static final ForgeConfigSpec.ConfigValue<String> MAGIC_NUMBER_INTRODUCTION = BUILDER
            .comment("What you want the introduction message to be for the magic number")
            .define("magicNumberIntroduction", "The magic number is... ");

    // a list of strings that are treated as resource locations for items
    private static final ForgeConfigSpec.ConfigValue<List<? extends String>> ITEM_STRINGS = BUILDER
            .comment("A list of items to log on common setup.")
            .defineListAllowEmpty("items", Arrays.asList("minecraft:iron_ingot"), Config::validateItemName);

    // Slot machine configuration
    private static final ForgeConfigSpec.ConfigValue<List<? extends String>> SLOT_MACHINE_ITEMS = BUILDER
            .comment("Items that can appear in the slot machine. Use resource location format (e.g., 'minecraft:diamond')")
            .defineListAllowEmpty("slotMachineItems", Arrays.asList(
                "minecraft:diamond",
                "minecraft:emerald",
                "minecraft:gold_ingot",
                "minecraft:iron_ingot",
                "minecraft:redstone",
                "minecraft:lapis_lazuli",
                "minecraft:coal",
                "minecraft:apple",
                "minecraft:bread",
                "minecraft:cooked_beef"
            ), Config::validateItemName);

    private static final ForgeConfigSpec.IntValue SPIN_DURATION = BUILDER
            .comment("Duration of slot machine spin animation in ticks (20 ticks = 1 second)")
            .defineInRange("spinDuration", 100, 20, 400);

    // Item Frame Display Configuration
    private static final ForgeConfigSpec.BooleanValue ENABLE_ITEM_FRAME_DISPLAY = BUILDER
            .comment("Enable displaying slot machine results on nearby Glow Item Frames")
            .define("enableItemFrameDisplay", true);

    private static final ForgeConfigSpec.IntValue ITEM_FRAME_SEARCH_RANGE = BUILDER
            .comment("Range in blocks to search for Glow Item Frames around the player")
            .defineInRange("itemFrameSearchRange", 10, 3, 50);

    private static final ForgeConfigSpec.BooleanValue CLEAR_FRAMES_ON_CLOSE = BUILDER
            .comment("Clear items from Glow Item Frames when the slot machine interface is closed (disabled by default - results are kept permanently)")
            .define("clearFramesOnClose", false);

    private static final ForgeConfigSpec.BooleanValue ENABLE_FLOATING_ITEMS = BUILDER
            .comment("Enable large floating item entities above Glow Item Frames for better visibility")
            .define("enableFloatingItems", true);

    private static final ForgeConfigSpec.BooleanValue USE_LARGE_ITEM_DISPLAY = BUILDER
            .comment("Use large 3x scaled Display Entity items instead of floating items for much better visibility")
            .define("useLargeItemDisplay", false);

    static final ForgeConfigSpec SPEC = BUILDER.build();

    public static boolean logDirtBlock;
    public static int magicNumber;
    public static String magicNumberIntroduction;
    public static Set<Item> items;
    public static Set<Item> slotMachineItems;
    public static int spinDuration;
    public static boolean enableItemFrameDisplay;
    public static int itemFrameSearchRange;
    public static boolean clearFramesOnClose;
    public static boolean enableFloatingItems;
    public static boolean useLargeItemDisplay;

    private static boolean validateItemName(final Object obj)
    {
        return obj instanceof final String itemName && ForgeRegistries.ITEMS.containsKey(ResourceLocation.tryParse(itemName));
    }

    @SubscribeEvent
    static void onLoad(final ModConfigEvent event)
    {
        logDirtBlock = LOG_DIRT_BLOCK.get();
        magicNumber = MAGIC_NUMBER.get();
        magicNumberIntroduction = MAGIC_NUMBER_INTRODUCTION.get();
        spinDuration = SPIN_DURATION.get();
        enableItemFrameDisplay = ENABLE_ITEM_FRAME_DISPLAY.get();
        itemFrameSearchRange = ITEM_FRAME_SEARCH_RANGE.get();
        clearFramesOnClose = CLEAR_FRAMES_ON_CLOSE.get();
        enableFloatingItems = ENABLE_FLOATING_ITEMS.get();
        useLargeItemDisplay = USE_LARGE_ITEM_DISPLAY.get();

        // convert the list of strings into a set of items
        items = ITEM_STRINGS.get().stream()
                .map(itemName -> ForgeRegistries.ITEMS.getValue(ResourceLocation.tryParse(itemName)))
                .collect(Collectors.toSet());

        // convert slot machine items
        slotMachineItems = SLOT_MACHINE_ITEMS.get().stream()
                .map(itemName -> ForgeRegistries.ITEMS.getValue(ResourceLocation.tryParse(itemName)))
                .collect(Collectors.toSet());
    }
}
